<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\GiftCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PaymentController extends Controller
{
    /**
     * Display a listing of the payments.
     */
    public function index(Request $request)
    {
        $query = Payment::query();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('payment_status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by customer info or payment ID
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%")
                  ->orWhere('payment_id', 'like', "%{$search}%")
                  ->orWhere('order_reference', 'like', "%{$search}%");
            });
        }

        $payments = $query->latest()->paginate(15);

        // Calculate statistics
        $stats = [
            'total' => Payment::count(),
            'paid' => Payment::paid()->count(),
            'pending' => Payment::pending()->count(),
            'failed' => Payment::failed()->count(),
            'total_amount' => Payment::paid()->sum('amount'),
            'today_amount' => Payment::paid()->today()->sum('amount'),
            'month_amount' => Payment::paid()->thisMonth()->sum('amount'),
        ];

        return view('admin.payments.index', compact('payments', 'stats'));
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment)
    {
        return view('admin.payments.show', compact('payment'));
    }

    /**
     * Update payment status
     */
    public function updateStatus(Request $request, Payment $payment)
    {
        $request->validate([
            'status' => 'required|in:PAID,FAILED,CANCELLED,EXPIRED'
        ]);

        $payment->update([
            'payment_status' => $request->status
        ]);

        return back()->with('success', 'تم تحديث حالة الدفع بنجاح.');
    }

    /**
     * Payment Analytics
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '30'); // Default 30 days

        $startDate = Carbon::now()->subDays($period);
        $endDate = Carbon::now();

        // Daily payments for chart
        $dailyPayments = Payment::paid()
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(amount) as total')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Payment methods breakdown
        $paymentMethods = Payment::paid()
            ->where('created_at', '>=', $startDate)
            ->select('payment_method')
            ->selectRaw('COUNT(*) as count, SUM(amount) as total')
            ->groupBy('payment_method')
            ->get();

        // Monthly comparison
        $thisMonth = Payment::paid()->thisMonth()->sum('amount');
        $lastMonth = Payment::paid()
            ->whereMonth('created_at', Carbon::now()->subMonth()->month)
            ->whereYear('created_at', Carbon::now()->subMonth()->year)
            ->sum('amount');

        $monthlyGrowth = $lastMonth > 0 ? (($thisMonth - $lastMonth) / $lastMonth) * 100 : 0;

        // Top customers
        $topCustomers = Payment::paid()
            ->where('created_at', '>=', $startDate)
            ->select('customer_name', 'customer_email')
            ->selectRaw('COUNT(*) as payment_count, SUM(amount) as total_amount')
            ->groupBy('customer_name', 'customer_email')
            ->orderBy('total_amount', 'desc')
            ->limit(10)
            ->get();

        return view('admin.payments.analytics', compact(
            'dailyPayments', 
            'paymentMethods', 
            'thisMonth', 
            'lastMonth', 
            'monthlyGrowth', 
            'topCustomers',
            'period'
        ));
    }

    /**
     * Export payments
     */
    public function export(Request $request)
    {
        $query = Payment::query();

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('payment_status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%")
                  ->orWhere('payment_id', 'like', "%{$search}%");
            });
        }

        $payments = $query->latest()->get();

        $filename = 'payments_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename={$filename}",
        ];

        $callback = function() use ($payments) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");
            
            // Header row
            fputcsv($file, [
                'رقم الدفع',
                'المرجع',
                'اسم العميل',
                'البريد الإلكتروني',
                'رقم الهاتف',
                'المبلغ',
                'طريقة الدفع',
                'الحالة',
                'تاريخ الإنشاء'
            ]);

            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment->payment_id,
                    $payment->order_reference,
                    $payment->customer_name,
                    $payment->customer_email,
                    $payment->customer_phone,
                    $payment->amount,
                    $payment->payment_method,
                    $payment->status_display_name,
                    $payment->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:mark_paid,mark_failed,delete',
            'payments' => 'required|array',
            'payments.*' => 'exists:payments,id'
        ]);

        $payments = Payment::whereIn('id', $request->payments);

        switch ($request->action) {
            case 'mark_paid':
                $payments->update(['payment_status' => Payment::STATUS_PAID]);
                $message = 'تم تحديث المدفوعات المحددة كمدفوعة.';
                break;
                
            case 'mark_failed':
                $payments->update(['payment_status' => Payment::STATUS_FAILED]);
                $message = 'تم تحديث المدفوعات المحددة كفاشلة.';
                break;
                
            case 'delete':
                $payments->delete();
                $message = 'تم حذف المدفوعات المحددة.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Delete a payment
     */
    public function destroy(Payment $payment)
    {
        $payment->delete();
        return back()->with('success', 'تم حذف الدفعة بنجاح.');
    }
}
