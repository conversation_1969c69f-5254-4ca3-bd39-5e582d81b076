@extends('layouts.admin')

@section('title', 'تحليلات المدفوعات')

@section('content')
<style>
.analytics-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #ff6b35;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.1);
    transition: all 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(255, 107, 53, 0.2);
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-box {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #ff6b35;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.stat-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 10px;
}

.stat-label {
    color: #ccc;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 5px;
}

.stat-growth {
    font-size: 0.9rem;
    font-weight: 600;
}

.growth-positive { color: #28a745; }
.growth-negative { color: #dc3545; }
.growth-neutral { color: #6c757d; }

.chart-container {
    background: rgba(42, 42, 42, 0.5);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    height: 400px;
    position: relative;
}

.chart-title {
    color: #ff6b35;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.2rem;
}

.period-selector {
    background: rgba(42, 42, 42, 0.8);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.period-btn {
    background: transparent;
    border: 1px solid #555;
    color: #ccc;
    padding: 8px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.period-btn.active,
.period-btn:hover {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border-color: #ff6b35;
    color: white;
}

.method-card {
    background: rgba(42, 42, 42, 0.5);
    border: 1px solid #555;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.method-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.method-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.method-details h6 {
    color: white;
    margin-bottom: 5px;
}

.method-stats {
    color: #ccc;
    font-size: 0.9rem;
}

.method-amount {
    text-align: right;
}

.method-amount .amount {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ff6b35;
}

.method-amount .count {
    color: #ccc;
    font-size: 0.9rem;
}

.customer-item {
    background: rgba(42, 42, 42, 0.5);
    border: 1px solid #555;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.customer-info h6 {
    color: white;
    margin-bottom: 3px;
}

.customer-email {
    color: #ccc;
    font-size: 0.9rem;
}

.customer-stats {
    text-align: right;
}

.customer-amount {
    font-weight: bold;
    color: #ff6b35;
}

.customer-count {
    color: #ccc;
    font-size: 0.9rem;
}

.btn-gradient {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
    color: white;
}
</style>

<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-chart-line me-2"></i>
        تحليلات المدفوعات
    </h1>
    <div class="page-actions">
        <a href="{{ route('admin.payments.index') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للمدفوعات
        </a>
        <button onclick="exportAnalytics()" class="btn btn-gradient">
            <i class="fas fa-download me-2"></i>
            تصدير التحليلات
        </button>
    </div>
</div>

<!-- Period Selector -->
<div class="period-selector">
    <span class="text-white me-3">عرض بيانات:</span>
    <button class="period-btn {{ $period == '7' ? 'active' : '' }}" onclick="changePeriod(7)">
        آخر 7 أيام
    </button>
    <button class="period-btn {{ $period == '30' ? 'active' : '' }}" onclick="changePeriod(30)">
        آخر 30 يوم
    </button>
    <button class="period-btn {{ $period == '90' ? 'active' : '' }}" onclick="changePeriod(90)">
        آخر 3 أشهر
    </button>
    <button class="period-btn {{ $period == '365' ? 'active' : '' }}" onclick="changePeriod(365)">
        آخر سنة
    </button>
</div>

<!-- Statistics Overview -->
<div class="stats-overview">
    <div class="stat-box">
        <div class="stat-number">{{ number_format($thisMonth, 2) }}</div>
        <div class="stat-label">إيرادات هذا الشهر</div>
        <div class="stat-growth {{ $monthlyGrowth >= 0 ? 'growth-positive' : 'growth-negative' }}">
            <i class="fas fa-{{ $monthlyGrowth >= 0 ? 'arrow-up' : 'arrow-down' }}"></i>
            {{ number_format(abs($monthlyGrowth), 1) }}% مقارنة بالشهر السابق
        </div>
    </div>
    
    <div class="stat-box">
        <div class="stat-number">{{ $dailyPayments->sum('count') }}</div>
        <div class="stat-label">إجمالي المعاملات</div>
        <div class="stat-growth growth-neutral">
            في آخر {{ $period }} يوم
        </div>
    </div>
    
    <div class="stat-box">
        <div class="stat-number">{{ number_format($dailyPayments->sum('total'), 2) }}</div>
        <div class="stat-label">إجمالي المبالغ (ريال)</div>
        <div class="stat-growth growth-neutral">
            في آخر {{ $period }} يوم
        </div>
    </div>
    
    <div class="stat-box">
        <div class="stat-number">{{ number_format($dailyPayments->avg('total'), 2) }}</div>
        <div class="stat-label">متوسط المعاملة اليومية</div>
        <div class="stat-growth growth-neutral">
            في آخر {{ $period }} يوم
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <!-- Daily Payments Chart -->
    <div class="col-lg-8 mb-4">
        <div class="analytics-card">
            <div class="chart-title">المدفوعات اليومية</div>
            <div class="chart-container">
                <canvas id="dailyPaymentsChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Payment Methods Chart -->
    <div class="col-lg-4 mb-4">
        <div class="analytics-card">
            <div class="chart-title">طرق الدفع</div>
            <div class="chart-container">
                <canvas id="paymentMethodsChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Payment Methods Details -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="analytics-card">
            <h5 class="text-white mb-4">
                <i class="fas fa-credit-card me-2"></i>
                تفصيل طرق الدفع
            </h5>
            
            @forelse($paymentMethods as $method)
                <div class="method-card">
                    <div class="method-info">
                        <div class="method-icon">
                            <i class="fas fa-{{ $method->payment_method == 'VISA' ? 'cc-visa' : ($method->payment_method == 'MADA' ? 'credit-card' : 'cc-mastercard') }}"></i>
                        </div>
                        <div class="method-details">
                            <h6>{{ $method->payment_method ?? 'غير محدد' }}</h6>
                            <div class="method-stats">{{ $method->count }} معاملة</div>
                        </div>
                    </div>
                    <div class="method-amount">
                        <div class="amount">{{ number_format($method->total, 2) }} ريال</div>
                        <div class="count">{{ number_format(($method->total / $dailyPayments->sum('total')) * 100, 1) }}%</div>
                    </div>
                </div>
            @empty
                <div class="text-center py-4">
                    <i class="fas fa-credit-card fa-2x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد بيانات طرق دفع</p>
                </div>
            @endforelse
        </div>
    </div>
    
    <!-- Top Customers -->
    <div class="col-lg-6 mb-4">
        <div class="analytics-card">
            <h5 class="text-white mb-4">
                <i class="fas fa-users me-2"></i>
                أفضل العملاء
            </h5>
            
            @forelse($topCustomers as $customer)
                <div class="customer-item">
                    <div class="customer-info">
                        <h6>{{ $customer->customer_name }}</h6>
                        <div class="customer-email">{{ $customer->customer_email }}</div>
                    </div>
                    <div class="customer-stats">
                        <div class="customer-amount">{{ number_format($customer->total_amount, 2) }} ريال</div>
                        <div class="customer-count">{{ $customer->payment_count }} معاملة</div>
                    </div>
                </div>
            @empty
                <div class="text-center py-4">
                    <i class="fas fa-users fa-2x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد بيانات عملاء</p>
                </div>
            @endforelse
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Daily Payments Chart
const dailyPaymentsCtx = document.getElementById('dailyPaymentsChart').getContext('2d');
const dailyPaymentsChart = new Chart(dailyPaymentsCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($dailyPayments->pluck('date')->map(function($date) { return \Carbon\Carbon::parse($date)->format('m/d'); })) !!},
        datasets: [{
            label: 'المبلغ (ريال)',
            data: {!! json_encode($dailyPayments->pluck('total')) !!},
            borderColor: '#ff6b35',
            backgroundColor: 'rgba(255, 107, 53, 0.1)',
            tension: 0.4,
            fill: true,
            yAxisID: 'y'
        }, {
            label: 'عدد المعاملات',
            data: {!! json_encode($dailyPayments->pluck('count')) !!},
            borderColor: '#f7931e',
            backgroundColor: 'rgba(247, 147, 30, 0.1)',
            tension: 0.4,
            fill: false,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            x: {
                ticks: { color: '#ccc' },
                grid: { color: '#333' }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                ticks: { color: '#ccc' },
                grid: { color: '#333' }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                ticks: { color: '#ccc' },
                grid: { drawOnChartArea: false }
            }
        },
        plugins: {
            legend: {
                labels: { color: '#fff' }
            }
        }
    }
});

// Payment Methods Chart
const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
const paymentMethodsChart = new Chart(paymentMethodsCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode($paymentMethods->pluck('payment_method')->map(function($method) { return $method ?? 'غير محدد'; })) !!},
        datasets: [{
            data: {!! json_encode($paymentMethods->pluck('total')) !!},
            backgroundColor: [
                '#ff6b35',
                '#f7931e',
                '#28a745',
                '#17a2b8',
                '#6c757d',
                '#dc3545'
            ],
            borderWidth: 2,
            borderColor: '#1a1a1a'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: { 
                    color: '#fff',
                    padding: 20
                }
            }
        }
    }
});

function changePeriod(days) {
    window.location.href = `{{ route('admin.payments.analytics') }}?period=${days}`;
}

function exportAnalytics() {
    // Create a comprehensive analytics export
    const exportData = {
        period: '{{ $period }}',
        daily_payments: {!! json_encode($dailyPayments) !!},
        payment_methods: {!! json_encode($paymentMethods) !!},
        top_customers: {!! json_encode($topCustomers) !!},
        monthly_growth: {{ $monthlyGrowth }},
        this_month: {{ $thisMonth }},
        last_month: {{ $lastMonth }}
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `payment_analytics_${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
</script>
@endpush 