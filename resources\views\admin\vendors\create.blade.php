@extends('layouts.admin')

@section('page-title', 'إضافة تاجر جديد')

@push('styles')
<style>
    .create-vendor-container {
        background: transparent;
        min-height: 100vh;
        padding: 20px 0;
    }

    .form-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 25px;
        padding: 40px;
        margin-bottom: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        position: relative;
        overflow: hidden;
    }

    .form-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border-radius: 25px 25px 0 0;
    }

    .page-title {
        color: white;
        font-weight: 800;
        font-size: 2.2rem;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-align: center;
        margin-bottom: 30px;
    }

    .form-label {
        color: #e0e0e0;
        font-weight: 600;
        margin-bottom: 8px;
        font-size: 0.95rem;
    }

    .form-control,
    .form-select,
    .form-check-input {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 12px;
        color: white;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus,
    .form-select:focus {
        background: rgba(255, 255, 255, 0.08);
        border-color: #ff6b35;
        box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        color: white;
    }

    .form-control::placeholder {
        color: #888;
    }

    .form-check-label {
        color: #e0e0e0;
        font-weight: 500;
    }

    .btn-primary {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        border: none;
        border-radius: 12px;
        padding: 12px 30px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        color: white;
    }

    .btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 12px 30px;
        font-weight: 600;
        color: #e0e0e0;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        transform: translateY(-2px);
    }

    .section-title {
        color: #ff6b35;
        font-weight: 700;
        font-size: 1.3rem;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid rgba(255, 107, 53, 0.2);
    }

    .invalid-feedback {
        color: #ff4757;
        font-size: 0.875rem;
        margin-top: 5px;
    }

    .form-control.is-invalid,
    .form-select.is-invalid {
        border-color: #ff4757;
        box-shadow: 0 0 0 0.2rem rgba(255, 71, 87, 0.25);
    }

    .alert {
        border-radius: 12px;
        border: none;
        padding: 15px 20px;
        margin-bottom: 25px;
    }

    .alert-success {
        background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 191, 149, 0.1) 100%);
        border-left: 4px solid #00d4aa;
        color: #00d4aa;
    }

    .alert-danger {
        background: linear-gradient(135deg, rgba(255, 71, 87, 0.1) 0%, rgba(255, 59, 59, 0.1) 100%);
        border-left: 4px solid #ff4757;
        color: #ff4757;
    }

    .back-btn {
        position: absolute;
        top: 20px;
        left: 20px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        padding: 10px 15px;
        color: #e0e0e0;
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .back-btn:hover {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        transform: translateY(-2px);
    }

    .file-upload-zone {
        border: 2px dashed rgba(255, 107, 53, 0.3);
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        background: rgba(255, 107, 53, 0.05);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .file-upload-zone:hover {
        border-color: rgba(255, 107, 53, 0.5);
        background: rgba(255, 107, 53, 0.08);
    }

    .file-upload-zone i {
        color: #ff6b35;
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .file-upload-text {
        color: #e0e0e0;
        font-weight: 500;
    }

    .required {
        color: #ff4757;
    }
</style>
@endpush

@section('content')
<div class="create-vendor-container">
    <a href="{{ route('admin.vendors.index') }}" class="back-btn">
        <i class="fas fa-arrow-right"></i> العودة للقائمة
    </a>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-card">
                    <h1 class="page-title">إضافة تاجر جديد</h1>

                    @if(session('success'))
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {{ session('error') }}
                        </div>
                    @endif

                    <form method="POST" action="{{ route('admin.vendors.store') }}" enctype="multipart/form-data">
                        @csrf

                        <!-- Basic Information -->
                        <div class="section-title">
                            <i class="fas fa-user me-2"></i>
                            المعلومات الأساسية
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم التاجر <span class="required">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="required">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email') }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم <span class="required">*</span></label>
                                <input type="text" class="form-control @error('username') is-invalid @enderror" 
                                       id="username" name="username" value="{{ old('username') }}" required>
                                @error('username')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="phone_number" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control @error('phone_number') is-invalid @enderror" 
                                       id="phone_number" name="phone_number" value="{{ old('phone_number') }}">
                                @error('phone_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور <span class="required">*</span></label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                       id="password" name="password" required>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="password_confirmation" class="form-label">تأكيد كلمة المرور <span class="required">*</span></label>
                                <input type="password" class="form-control" 
                                       id="password_confirmation" name="password_confirmation" required>
                            </div>
                        </div>

                        <!-- Company Information -->
                        <div class="section-title mt-4">
                            <i class="fas fa-building me-2"></i>
                            معلومات الشركة
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">اسم الشركة <span class="required">*</span></label>
                                <input type="text" class="form-control @error('company_name') is-invalid @enderror" 
                                       id="company_name" name="company_name" value="{{ old('company_name') }}" required>
                                @error('company_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">فئة الشركة</label>
                                <input type="text" class="form-control @error('category') is-invalid @enderror" 
                                       id="category" name="category" value="{{ old('category') }}" 
                                       placeholder="مثال: إلكترونيات، أزياء، مجوهرات">
                                @error('category')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="website" class="form-label">موقع الشركة</label>
                                <input type="url" class="form-control @error('website') is-invalid @enderror" 
                                       id="website" name="website" value="{{ old('website') }}" 
                                       placeholder="https://example.com">
                                @error('website')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="logo" class="form-label">شعار الشركة</label>
                                <input type="file" class="form-control @error('logo') is-invalid @enderror" 
                                       id="logo" name="logo" accept="image/*">
                                @error('logo')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">عنوان الشركة</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" name="address" rows="3">{{ old('address') }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف الشركة</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" 
                                      placeholder="وصف مختصر عن أنشطة الشركة...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Social Media -->
                        <div class="section-title mt-4">
                            <i class="fas fa-share-alt me-2"></i>
                            وسائل التواصل الاجتماعي
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="facebook" class="form-label">فيسبوك</label>
                                <input type="url" class="form-control @error('facebook') is-invalid @enderror" 
                                       id="facebook" name="facebook" value="{{ old('facebook') }}" 
                                       placeholder="https://facebook.com/page">
                                @error('facebook')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="instagram" class="form-label">إنستغرام</label>
                                <input type="url" class="form-control @error('instagram') is-invalid @enderror" 
                                       id="instagram" name="instagram" value="{{ old('instagram') }}" 
                                       placeholder="https://instagram.com/account">
                                @error('instagram')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="twitter" class="form-label">تويتر</label>
                                <input type="url" class="form-control @error('twitter') is-invalid @enderror" 
                                       id="twitter" name="twitter" value="{{ old('twitter') }}" 
                                       placeholder="https://twitter.com/account">
                                @error('twitter')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Banking Information -->
                        <div class="section-title mt-4">
                            <i class="fas fa-university me-2"></i>
                            المعلومات المصرفية
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="bank_name" class="form-label">اسم البنك</label>
                                <input type="text" class="form-control @error('bank_name') is-invalid @enderror" 
                                       id="bank_name" name="bank_name" value="{{ old('bank_name') }}">
                                @error('bank_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="account_name" class="form-label">اسم صاحب الحساب</label>
                                <input type="text" class="form-control @error('account_name') is-invalid @enderror" 
                                       id="account_name" name="account_name" value="{{ old('account_name') }}">
                                @error('account_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="account_number" class="form-label">رقم الحساب</label>
                                <input type="text" class="form-control @error('account_number') is-invalid @enderror" 
                                       id="account_number" name="account_number" value="{{ old('account_number') }}">
                                @error('account_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="iban" class="form-label">رقم الآيبان (IBAN)</label>
                                <input type="text" class="form-control @error('iban') is-invalid @enderror" 
                                       id="iban" name="iban" value="{{ old('iban') }}">
                                @error('iban')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="section-title mt-4">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="auto_approve" 
                                       name="auto_approve" value="1" {{ old('auto_approve') ? 'checked' : '' }}>
                                <label class="form-check-label" for="auto_approve">
                                    تفعيل التاجر تلقائياً
                                </label>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between align-items-center mt-5">
                            <a href="{{ route('admin.vendors.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التاجر
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection