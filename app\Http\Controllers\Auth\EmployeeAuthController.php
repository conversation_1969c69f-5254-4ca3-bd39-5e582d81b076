<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\Employee;

class EmployeeAuthController extends Controller
{
    /**
     * Show the employee login form.
     */
    public function showLogin()
    {
        if (Auth::guard('employee')->check()) {
            return redirect()->route('employee.dashboard');
        }

        return view('employee.auth.login');
    }

    /**
     * Handle employee login.
     */
    public function login(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|string',
            'password' => 'required|string',
        ]);

        // Try to find employee by employee_id or phone
        $employee = Employee::where('employee_id', $request->employee_id)
                          ->orWhere('phone_number', $request->employee_id)
                          ->first();

        if ($employee && Hash::check($request->password, $employee->password)) {
            Auth::guard('employee')->login($employee, $request->filled('remember'));
            
            return redirect()->intended(route('employee.dashboard'))
                           ->with('success', 'مرحباً ' . $employee->name);
        }

        return back()->withErrors([
            'employee_id' => 'بيانات الدخول غير صحيحة.',
        ])->withInput($request->only('employee_id'));
    }

    /**
     * Handle employee logout.
     */
    public function logout(Request $request)
    {
        Auth::guard('employee')->logout();
        
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('employee.login')
                       ->with('success', 'تم تسجيل الخروج بنجاح');
    }
} 