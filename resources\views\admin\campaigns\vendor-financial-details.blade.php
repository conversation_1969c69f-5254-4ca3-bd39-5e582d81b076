@extends('layouts.admin')

@section('title', 'التفاصيل المالية - ' . $vendor->company_name)

@section('content')
<style>
.vendor-profile-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #ff6b35;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 60px rgba(255, 107, 53, 0.1);
}

.vendor-avatar-large {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 2.5rem;
    margin: 0 auto 20px;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-stat {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #ff6b35;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.1);
    transition: all 0.3s ease;
}

.summary-stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(255, 107, 53, 0.2);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 1.5rem;
    color: white;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 10px;
}

.stat-label {
    color: #ccc;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.campaign-table {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #333;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 30px;
}

.table-header {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    padding: 20px;
    font-weight: bold;
    text-align: center;
}

.campaign-row {
    border-bottom: 1px solid #333;
    padding: 20px;
    transition: all 0.3s ease;
}

.campaign-row:hover {
    background: rgba(255, 107, 53, 0.05);
}

.campaign-row:last-child {
    border-bottom: none;
}

.campaign-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.campaign-name {
    color: #ff6b35;
    font-weight: 700;
    font-size: 1.1rem;
}

.campaign-stage {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.stage-completed { background: rgba(40, 167, 69, 0.2); color: #28a745; }
.stage-processing { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
.stage-approved { background: rgba(23, 162, 184, 0.2); color: #17a2b8; }
.stage-pending_approval { background: rgba(108, 117, 125, 0.2); color: #6c757d; }

.campaign-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.metric-item {
    text-align: center;
    padding: 10px;
    background: rgba(42, 42, 42, 0.5);
    border-radius: 8px;
}

.metric-value {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.metric-label {
    color: #ccc;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.financial-breakdown {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #ff6b35;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
}

.breakdown-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.breakdown-item {
    background: rgba(255, 107, 53, 0.1);
    border: 1px solid rgba(255, 107, 53, 0.3);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
}

.breakdown-amount {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.amount-positive { color: #28a745; }
.amount-negative { color: #dc3545; }
.amount-warning { color: #ffc107; }
.amount-info { color: #17a2b8; }

.btn-gradient {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
    color: white;
}
</style>

<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-user-tie me-2"></i>
        التفاصيل المالية - {{ $vendor->company_name }}
    </h1>
    <div class="page-actions">
        <a href="{{ route('admin.campaigns.financial-reports') }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للتقارير
        </a>
        <button onclick="printReport()" class="btn btn-gradient">
            <i class="fas fa-print me-2"></i>
            طباعة التقرير
        </button>
    </div>
</div>

<!-- Vendor Profile -->
<div class="vendor-profile-card">
    <div class="row align-items-center">
        <div class="col-md-2 text-center">
            <div class="vendor-avatar-large">
                {{ mb_substr($vendor->company_name, 0, 1) }}
            </div>
        </div>
        <div class="col-md-10">
            <h2 class="text-white mb-2">{{ $vendor->company_name }}</h2>
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-1"><i class="fas fa-user me-2"></i>{{ $vendor->contact_person }}</p>
                    <p class="text-muted mb-1"><i class="fas fa-phone me-2"></i>{{ $vendor->phone }}</p>
                    <p class="text-muted mb-1"><i class="fas fa-envelope me-2"></i>{{ $vendor->email }}</p>
                </div>
                <div class="col-md-6">
                    <p class="text-muted mb-1"><i class="fas fa-map-marker-alt me-2"></i>{{ $vendor->address }}</p>
                    <p class="text-muted mb-1"><i class="fas fa-calendar me-2"></i>عضو منذ {{ $vendor->created_at->format('Y/m/d') }}</p>
                    <p class="text-muted mb-1"><i class="fas fa-check-circle me-2 text-success"></i>حساب معتمد</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summary Statistics -->
<div class="summary-stats">
    <div class="summary-stat">
        <div class="stat-icon">
            <i class="fas fa-bullhorn"></i>
        </div>
        <div class="stat-number">{{ $summary['total_campaigns'] }}</div>
        <div class="stat-label">إجمالي الحملات</div>
    </div>
    
    <div class="summary-stat">
        <div class="stat-icon">
            <i class="fas fa-envelope"></i>
        </div>
        <div class="stat-number">{{ number_format($summary['total_messages_sent']) }}</div>
        <div class="stat-label">الرسائل المرسلة</div>
    </div>
    
    <div class="summary-stat">
        <div class="stat-icon">
            <i class="fas fa-gift"></i>
        </div>
        <div class="stat-number">{{ number_format($summary['total_gifts_claimed']) }}</div>
        <div class="stat-label">الهدايا المستلمة</div>
    </div>
    
    <div class="summary-stat">
        <div class="stat-icon">
            <i class="fas fa-percentage"></i>
        </div>
        <div class="stat-number">{{ $summary['commission_rate'] }}%</div>
        <div class="stat-label">نسبة العمولة</div>
    </div>
</div>

<!-- Financial Breakdown -->
<div class="financial-breakdown">
    <h4 class="text-white mb-4">
        <i class="fas fa-calculator me-2"></i>
        التفصيل المالي
    </h4>
    
    <div class="breakdown-grid">
        <div class="breakdown-item">
            <div class="breakdown-amount amount-info">
                {{ number_format($summary['total_campaign_costs'], 2) }} ريال
            </div>
            <div class="stat-label">إجمالي تكلفة الحملات</div>
            <small class="text-muted d-block mt-1">المبلغ المدفوع للرسائل والإعلانات</small>
        </div>
        
        <div class="breakdown-item">
            <div class="breakdown-amount amount-positive">
                {{ number_format($summary['total_commission_earned'], 2) }} ريال
            </div>
            <div class="stat-label">إجمالي العمولات المكتسبة</div>
            <small class="text-muted d-block mt-1">{{ $summary['commission_rate'] }}% من قيمة الهدايا المستلمة</small>
        </div>
        
        <div class="breakdown-item">
            <div class="breakdown-amount amount-warning">
                {{ number_format($summary['pending_commissions'], 2) }} ريال
            </div>
            <div class="stat-label">العمولات المعلقة</div>
            <small class="text-muted d-block mt-1">من الحملات غير المكتملة</small>
        </div>
        
        <div class="breakdown-item">
            <div class="breakdown-amount {{ ($summary['total_campaign_costs'] - $summary['total_commission_earned']) >= 0 ? 'amount-negative' : 'amount-positive' }}">
                {{ number_format($summary['total_campaign_costs'] - $summary['total_commission_earned'], 2) }} ريال
            </div>
            <div class="stat-label">صافي الربح/الخسارة</div>
            <small class="text-muted d-block mt-1">التكلفة مطروحاً منها العمولات</small>
        </div>
    </div>
</div>

<!-- Campaigns Details -->
<div class="campaign-table">
    <div class="table-header">
        <h4 class="mb-0">
            <i class="fas fa-list me-2"></i>
            تفاصيل الحملات الإعلانية
        </h4>
    </div>
    
    @forelse($campaigns as $campaign)
        <div class="campaign-row">
            <div class="campaign-info">
                <div>
                    <div class="campaign-name">{{ $campaign->campaign_name ?? 'حملة ' . $campaign->gift->name }}</div>
                    <small class="text-muted">{{ $campaign->created_at->format('Y/m/d H:i') }}</small>
                </div>
                <div class="campaign-stage stage-{{ $campaign->stage }}">
                    {{ $campaign->getStageDisplayName() }}
                </div>
            </div>
            
            <div class="campaign-metrics">
                <div class="metric-item">
                    <div class="metric-value text-info">{{ number_format($campaign->target_client_count) }}</div>
                    <div class="metric-label">العملاء المستهدفون</div>
                </div>
                
                <div class="metric-item">
                    <div class="metric-value text-warning">{{ number_format($campaign->sent_count) }}</div>
                    <div class="metric-label">الرسائل المرسلة</div>
                </div>
                
                <div class="metric-item">
                    <div class="metric-value text-success">{{ number_format($campaign->gifts_claimed_count ?? 0) }}</div>
                    <div class="metric-label">الهدايا المستلمة</div>
                </div>
                
                <div class="metric-item">
                    <div class="metric-value text-primary">{{ number_format($campaign->total_cost, 2) }} ريال</div>
                    <div class="metric-label">تكلفة الحملة</div>
                </div>
                
                <div class="metric-item">
                    <div class="metric-value text-success">
                        {{ number_format(($campaign->gift->price * 0.15) * ($campaign->gifts_claimed_count ?? 0), 2) }} ريال
                    </div>
                    <div class="metric-label">العمولة المكتسبة</div>
                </div>
                
                <div class="metric-item">
                    <div class="metric-value {{ ($campaign->gifts_claimed_count ?? 0) > 0 ? 'text-success' : 'text-muted' }}">
                        {{ ($campaign->gifts_claimed_count ?? 0) > 0 ? number_format((($campaign->gifts_claimed_count ?? 0) / max($campaign->sent_count, 1)) * 100, 1) . '%' : '0%' }}
                    </div>
                    <div class="metric-label">معدل الاستجابة</div>
                </div>
            </div>
        </div>
    @empty
        <div class="campaign-row text-center">
            <div class="text-muted py-4">
                <i class="fas fa-bullhorn fa-2x mb-3"></i>
                <p>لا توجد حملات إعلانية لهذا التاجر</p>
            </div>
        </div>
    @endforelse
</div>

@endsection

@push('scripts')
<script>
function printReport() {
    window.print();
}

// Add print styles
const printStyles = `
    @media print {
        .page-header, .btn, .page-actions { display: none !important; }
        .campaign-table, .financial-breakdown, .vendor-profile-card { 
            break-inside: avoid; 
            page-break-inside: avoid; 
        }
        body { background: white !important; }
        * { color: black !important; }
    }
`;

const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);
</script>
@endpush 