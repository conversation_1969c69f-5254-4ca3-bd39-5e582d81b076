@extends('layouts.vendor')

@section('title', 'إضافة موظف جديد')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-xl-8">
            <div class="create-form-container">
                <div class="create-form-header">
                    <h1>
                        <i class="fas fa-user-plus me-3"></i>
                        إضافة موظف جديد
                    </h1>
                    <p>أضف موظف جديد إلى فريقك لمساعدتك في تسليم الهدايا وإدارة العمليات</p>
                </div>
                
                <div class="create-form-body">
                    <form action="{{ route('vendor.employees.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf

                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <!-- Personal Information Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <i class="fas fa-user"></i>
                                المعلومات الشخصية
                            </h3>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name" class="form-label">
                                            <i class="fas fa-id-card"></i>
                                            اسم الموظف *
                                        </label>
                                        <input type="text" 
                                               id="name" 
                                               name="name" 
                                               class="form-control @error('name') is-invalid @enderror" 
                                               value="{{ old('name') }}" 
                                               placeholder="أدخل الاسم الكامل للموظف"
                                               required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="national_id" class="form-label">
                                            <i class="fas fa-id-badge"></i>
                                            رقم الهوية الوطنية *
                                        </label>
                                        <input type="text" 
                                               id="national_id" 
                                               name="national_id" 
                                               class="form-control @error('national_id') is-invalid @enderror" 
                                               value="{{ old('national_id') }}" 
                                               placeholder="1234567890"
                                               required>
                                        @error('national_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="phone" class="form-label">
                                            <i class="fas fa-phone"></i>
                                            رقم الهاتف *
                                        </label>
                                        <input type="tel" 
                                               id="phone" 
                                               name="phone" 
                                               class="form-control @error('phone') is-invalid @enderror" 
                                               value="{{ old('phone') }}" 
                                               placeholder="05xxxxxxxx"
                                               required>
                                        @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope"></i>
                                            البريد الإلكتروني *
                                        </label>
                                        <input type="email" 
                                               id="email" 
                                               name="email" 
                                               class="form-control @error('email') is-invalid @enderror" 
                                               value="{{ old('email') }}"
                                               placeholder="<EMAIL>"
                                               required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="gender" class="form-label">
                                            <i class="fas fa-venus-mars"></i>
                                            الجنس *
                                        </label>
                                        <select id="gender" 
                                                name="gender" 
                                                class="form-select @error('gender') is-invalid @enderror" 
                                                required>
                                            <option value="">اختر الجنس</option>
                                            <option value="male" {{ old('gender') == 'male' ? 'selected' : '' }}>ذكر</option>
                                            <option value="female" {{ old('gender') == 'female' ? 'selected' : '' }}>أنثى</option>
                                        </select>
                                        @error('gender')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="birth_date" class="form-label">
                                            <i class="fas fa-calendar"></i>
                                            تاريخ الميلاد *
                                        </label>
                                        <input type="date" 
                                               id="birth_date" 
                                               name="birth_date" 
                                               class="form-control @error('birth_date') is-invalid @enderror" 
                                               value="{{ old('birth_date') }}" 
                                               required>
                                        @error('birth_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i>
                                    العنوان *
                                </label>
                                <textarea id="address" 
                                          name="address" 
                                          class="form-control @error('address') is-invalid @enderror" 
                                          rows="3"
                                          placeholder="أدخل العنوان الكامل للموظف"
                                          required>{{ old('address') }}</textarea>
                                @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Login Information Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <i class="fas fa-key"></i>
                                معلومات تسجيل الدخول
                            </h3>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="password" class="form-label">
                                            <i class="fas fa-lock"></i>
                                            كلمة المرور *
                                        </label>
                                        <input type="password"
                                               id="password"
                                               name="password"
                                               class="form-control @error('password') is-invalid @enderror"
                                               placeholder="أدخل كلمة مرور قوية للموظف"
                                               required>
                                        @error('password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted mt-2">
                                            <i class="fas fa-info-circle"></i>
                                            يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="password_confirmation" class="form-label">
                                            <i class="fas fa-lock"></i>
                                            تأكيد كلمة المرور *
                                        </label>
                                        <input type="password"
                                               id="password_confirmation"
                                               name="password_confirmation"
                                               class="form-control @error('password_confirmation') is-invalid @enderror"
                                               placeholder="أعد إدخال كلمة المرور"
                                               required>
                                        @error('password_confirmation')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contact Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <i class="fas fa-phone-alt"></i>
                                معلومات الاتصال الطارئ
                            </h3>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="emergency_contact_name" class="form-label">
                                            <i class="fas fa-user-friends"></i>
                                            اسم جهة الاتصال الطارئ *
                                        </label>
                                        <input type="text" 
                                               id="emergency_contact_name" 
                                               name="emergency_contact_name" 
                                               class="form-control @error('emergency_contact_name') is-invalid @enderror" 
                                               value="{{ old('emergency_contact_name') }}" 
                                               placeholder="اسم الشخص للاتصال في حالة الطوارئ"
                                               required>
                                        @error('emergency_contact_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="emergency_contact_phone" class="form-label">
                                            <i class="fas fa-phone"></i>
                                            رقم هاتف الاتصال الطارئ *
                                        </label>
                                        <input type="tel" 
                                               id="emergency_contact_phone" 
                                               name="emergency_contact_phone" 
                                               class="form-control @error('emergency_contact_phone') is-invalid @enderror" 
                                               value="{{ old('emergency_contact_phone') }}" 
                                               placeholder="05xxxxxxxx"
                                               required>
                                        @error('emergency_contact_phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Job Information Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <i class="fas fa-briefcase"></i>
                                معلومات الوظيفة
                            </h3>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="position" class="form-label">
                                            <i class="fas fa-user-tie"></i>
                                            المنصب الوظيفي *
                                        </label>
                                        <select id="position" 
                                                name="position" 
                                                class="form-select @error('position') is-invalid @enderror" 
                                                required>
                                            <option value="">اختر المنصب الوظيفي</option>
                                            <option value="موظف تسليم" {{ old('position') == 'موظف تسليم' ? 'selected' : '' }}>موظف تسليم</option>
                                            <option value="مشرف تسليم" {{ old('position') == 'مشرف تسليم' ? 'selected' : '' }}>مشرف تسليم</option>
                                            <option value="مندوب مبيعات" {{ old('position') == 'مندوب مبيعات' ? 'selected' : '' }}>مندوب مبيعات</option>
                                            <option value="خدمة عملاء" {{ old('position') == 'خدمة عملاء' ? 'selected' : '' }}>خدمة عملاء</option>
                                        </select>
                                        @error('position')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="employment_type" class="form-label">
                                            <i class="fas fa-clock"></i>
                                            نوع التوظيف *
                                        </label>
                                        <select id="employment_type" 
                                                name="employment_type" 
                                                class="form-select @error('employment_type') is-invalid @enderror" 
                                                required>
                                            <option value="">اختر نوع التوظيف</option>
                                            <option value="full_time" {{ old('employment_type') == 'full_time' ? 'selected' : '' }}>دوام كامل</option>
                                            <option value="part_time" {{ old('employment_type') == 'part_time' ? 'selected' : '' }}>دوام جزئي</option>
                                            <option value="contract" {{ old('employment_type') == 'contract' ? 'selected' : '' }}>عقد مؤقت</option>
                                        </select>
                                        @error('employment_type')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="hire_date" class="form-label">
                                            <i class="fas fa-calendar-plus"></i>
                                            تاريخ التوظيف *
                                        </label>
                                        <input type="date" 
                                               id="hire_date" 
                                               name="hire_date" 
                                               class="form-control @error('hire_date') is-invalid @enderror" 
                                               value="{{ old('hire_date', date('Y-m-d')) }}" 
                                               required>
                                        @error('hire_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="salary" class="form-label">
                                            <i class="fas fa-money-bill-wave"></i>
                                            الراتب الشهري (ريال)
                                        </label>
                                        <input type="number" 
                                               id="salary" 
                                               name="salary" 
                                               class="form-control @error('salary') is-invalid @enderror" 
                                               value="{{ old('salary') }}" 
                                               step="0.01"
                                               min="0"
                                               placeholder="0.00">
                                        @error('salary')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Picture Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <i class="fas fa-camera"></i>
                                صورة الموظف
                            </h3>
                            <div class="form-group">
                                <label for="profile_photo" class="form-label">
                                    <i class="fas fa-upload"></i>
                                    رفع صورة شخصية
                                </label>
                                <input type="file" 
                                       id="profile_photo" 
                                       name="profile_photo" 
                                       class="form-control @error('profile_photo') is-invalid @enderror" 
                                       accept="image/*">
                                <small class="form-text text-muted mt-2">
                                    <i class="fas fa-info-circle"></i>
                                    يُفضل أن تكون الصورة واضحة بحجم لا يزيد عن 2MB
                                </small>
                                @error('profile_photo')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between align-items-center pt-4 border-top">
                            <a href="{{ route('vendor.employees.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-user-check"></i>
                                حفظ الموظف وإضافته للفريق
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 