<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Gift extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'status',
        'price',
        'category',
        'notes',
        'image',
        'approved',
        'vendor_id',
        'stock_quantity',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'approved' => 'boolean',
    ];

    /**
     * Get the vendor that owns the gift
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get clients associated with this gift
     */
    public function clients(): HasMany
    {
        return $this->hasMany(Client::class);
    }

    /**
     * Check if gift is approved
     */
    public function isApproved(): bool
    {
        return $this->approved;
    }

    /**
     * Check if gift is delivered
     */
    public function isDelivered(): bool
    {
        return $this->status === 'تم التسليم';
    }

    /**
     * Check if gift is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'قيد الانتظار';
    }

    /**
     * Check if gift is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === 'تم الرفض';
    }

    /**
     * Get the formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        return number_format($this->price, 2) . ' ريال';
    }

    /**
     * Get the gift image URL
     */
    public function getImageUrlAttribute(): ?string
    {
        return $this->image ? asset('storage/' . $this->image) : null;
    }

    /**
     * Scope for approved gifts
     */
    public function scopeApproved($query)
    {
        return $query->where('approved', true);
    }

    /**
     * Scope for pending gifts
     */
    public function scopePending($query)
    {
        return $query->where('approved', false);
    }

    /**
     * Scope for delivered gifts
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', 'تم التسليم');
    }

    /**
     * Scope for rejected gifts
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'تم الرفض');
    }

    /**
     * Scope for gifts by vendor
     */
    public function scopeByVendor($query, $vendorId)
    {
        return $query->where('vendor_id', $vendorId);
    }

    /**
     * Scope for gifts within price range
     */
    public function scopePriceRange($query, $min, $max)
    {
        return $query->whereBetween('price', [$min, $max]);
    }

    /**
     * Approve the gift
     */
    public function approve(): bool
    {
        $this->approved = true;
        $this->status = 'قيد الانتظار';
        return $this->save();
    }

    /**
     * Reject the gift
     */
    public function reject(): bool
    {
        $this->approved = false;
        $this->status = 'تم الرفض';
        return $this->save();
    }

    /**
     * Mark as delivered
     */
    public function markAsDelivered(): bool
    {
        $this->status = 'تم التسليم';
        return $this->save();
    }
}
