@extends('layouts.vendor')

@section('title', 'إضافة هدية جديدة')

@section('page-title', 'إضافة هدية جديدة')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="create-form-container">
                <div class="create-form-header">
                    <h1>
                        <i class="fas fa-gift me-2"></i>
                        إضافة هدية جديدة
                    </h1>
                    <p>أضف هدية جديدة إلى متجرك، ستحتاج موافقة الإدارة قبل النشر</p>
                </div>
                
                <div class="create-form-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <form action="{{ route('vendor.gifts.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf

                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <i class="fas fa-info-circle"></i>
                                المعلومات الأساسية
                            </h3>

                            <div class="form-group">
                                <label for="name" class="form-label">
                                    <i class="fas fa-tag"></i>
                                    اسم الهدية *
                                </label>
                                <input type="text" 
                                       id="name" 
                                       name="name" 
                                       class="form-control @error('name') is-invalid @enderror" 
                                       value="{{ old('name') }}" 
                                       placeholder="أدخل اسم الهدية"
                                       required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left"></i>
                                    وصف الهدية *
                                </label>
                                <textarea id="description" 
                                          name="description" 
                                          class="form-control @error('description') is-invalid @enderror" 
                                          rows="4" 
                                          placeholder="أضف وصفاً تفصيلياً للهدية"
                                          required>{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="category" class="form-label">
                                    <i class="fas fa-list"></i>
                                    الفئة *
                                </label>
                                <select id="category" 
                                        name="category" 
                                        class="form-select @error('category') is-invalid @enderror" 
                                        required>
                                    <option value="">اختر الفئة</option>
                                    <option value="electronics" {{ old('category') == 'electronics' ? 'selected' : '' }}>إلكترونيات</option>
                                    <option value="fashion" {{ old('category') == 'fashion' ? 'selected' : '' }}>أزياء وملابس</option>
                                    <option value="home" {{ old('category') == 'home' ? 'selected' : '' }}>منزل ومطبخ</option>
                                    <option value="beauty" {{ old('category') == 'beauty' ? 'selected' : '' }}>جمال وعناية</option>
                                    <option value="sports" {{ old('category') == 'sports' ? 'selected' : '' }}>رياضة وصحة</option>
                                    <option value="books" {{ old('category') == 'books' ? 'selected' : '' }}>كتب وثقافة</option>
                                    <option value="toys" {{ old('category') == 'toys' ? 'selected' : '' }}>ألعاب وترفيه</option>
                                    <option value="food" {{ old('category') == 'food' ? 'selected' : '' }}>طعام ومشروبات</option>
                                    <option value="other" {{ old('category') == 'other' ? 'selected' : '' }}>أخرى</option>
                                </select>
                                @error('category')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Pricing & Stock Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <i class="fas fa-money-bill-wave"></i>
                                التسعير والمخزون
                            </h3>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="price" class="form-label">
                                            <i class="fas fa-dollar-sign"></i>
                                            السعر (ريال) *
                                        </label>
                                        <input type="number" 
                                               id="price" 
                                               name="price" 
                                               class="form-control @error('price') is-invalid @enderror" 
                                               value="{{ old('price') }}" 
                                               placeholder="0.00"
                                               step="0.01"
                                               min="0"
                                               required>
                                        @error('price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="stock_quantity" class="form-label">
                                            <i class="fas fa-boxes"></i>
                                            الكمية المتوفرة *
                                        </label>
                                        <input type="number" 
                                               id="stock_quantity" 
                                               name="stock_quantity" 
                                               class="form-control @error('stock_quantity') is-invalid @enderror" 
                                               value="{{ old('stock_quantity') }}" 
                                               placeholder="0"
                                               min="0"
                                               required>
                                        @error('stock_quantity')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Image Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <i class="fas fa-image"></i>
                                صورة الهدية
                            </h3>

                            <div class="form-group">
                                <label for="image" class="form-label">
                                    <i class="fas fa-camera"></i>
                                    اختر صورة الهدية
                                </label>
                                <input type="file" 
                                       id="image" 
                                       name="image" 
                                       class="form-control @error('image') is-invalid @enderror" 
                                       accept="image/*"
                                       onchange="previewImage(this)">
                                @error('image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                
                                <div id="imagePreview" style="display: none; margin-top: 15px; text-align: center;">
                                    <img id="previewImg" src="" alt="معاينة الصورة" style="max-width: 200px; max-height: 200px; border-radius: 15px; border: 2px solid var(--vendor-secondary);">
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex gap-3 mt-4">
                            <a href="{{ route('vendor.gifts.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ الهدية
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        }
        
        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
    }
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, textarea, select');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
    });
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        inputs.forEach(input => {
            if (input.hasAttribute('required') && !input.value.trim()) {
                isValid = false;
                input.classList.add('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
        }
    });
});

function validateField(field) {
    if (field.hasAttribute('required') && !field.value.trim()) {
        field.classList.add('is-invalid');
    } else {
        field.classList.remove('is-invalid');
    }
}
</script>
@endpush 