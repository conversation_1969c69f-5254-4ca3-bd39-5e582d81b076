<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use App\Models\Vendor;

class VendorAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // Get the authenticated user
        $user = Auth::user();

        // Check if user has a vendor profile
        $vendor = Vendor::where('user_id', $user->id)->first();

        if (!$vendor) {
            abort(403, 'Access denied. You do not have a vendor profile.');
        }

        // Check if vendor is approved
        if (!$vendor->isApproved()) {
            abort(403, 'Access denied. Your vendor profile is not approved yet.');
        }

        // Add vendor to request for easy access in controllers
        $request->merge(['vendor' => $vendor]);

        return $next($request);
    }
}
