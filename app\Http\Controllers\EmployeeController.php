<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Client;
use App\Models\GiftDelivery;
use App\Models\Employee;
use App\Models\GiftCampaign;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class EmployeeController extends Controller
{
    /**
     * Get current employee - supports both employee guard and staff users
     */
    private function getCurrentEmployee()
    {
        // Try employee guard first
        $employee = Auth::guard('employee')->user();
        if ($employee) {
            return $employee;
        }

        // Try web guard for staff users
        $user = Auth::user();
        if ($user && $user->isStaff()) {
            // For staff users, we'll use the first employee record or create a dummy one
            $employee = Employee::first();
            if (!$employee) {
                // Create a dummy employee record for staff users
                $employee = (object)[
                    'id' => 0,
                    'name' => $user->name,
                    'email' => $user->email
                ];
            }
            return $employee;
        }

        return null;
    }

    /**
     * Employee dashboard
     */
    public function dashboard()
    {
        $employee = $this->getCurrentEmployee();
        
        if (!$employee) {
            return redirect()->route('employee.login');
        }

        // Get today's statistics
        $today = Carbon::today();
        $employeeId = $employee->id ?? 0;
        
        $stats = [
            'deliveries_today' => GiftDelivery::where('employee_id', $employeeId)
                                            ->whereDate('delivered_at', $today)
                                            ->where('status', 'delivered')
                                            ->count(),
            
            'pending_deliveries' => GiftDelivery::where('employee_id', $employeeId)
                                               ->where('status', 'assigned')
                                               ->count(),
            
            'total_deliveries' => GiftDelivery::where('employee_id', $employeeId)
                                            ->where('status', 'delivered')
                                            ->count(),
            
            'this_week' => GiftDelivery::where('employee_id', $employeeId)
                                     ->where('status', 'delivered')
                                     ->whereBetween('delivered_at', [
                                         Carbon::now()->startOfWeek(),
                                         Carbon::now()->endOfWeek()
                                     ])
                                     ->count()
        ];

        // Recent deliveries
        $recentDeliveries = GiftDelivery::where('employee_id', $employeeId)
                                      ->where('status', 'delivered')
                                      ->with(['client', 'gift'])
                                      ->orderBy('delivered_at', 'desc')
                                      ->take(5)
                                      ->get();

        return view('employee.dashboard', compact('stats', 'recentDeliveries'));
    }

    /**
     * Search for client by phone number
     */
    public function search(Request $request)
    {
        if (!$request->filled('phone')) {
            return view('employee.search');
        }

        $phone = $request->phone;
        
        // Find client by phone number
        $client = Client::where('phone', 'LIKE', "%{$phone}%")
                       ->orWhere('phone', 'LIKE', "%{$phone}")
                       ->orWhere('phone', 'LIKE', "{$phone}%")
                       ->first();

        if (!$client) {
            return view('employee.search', [
                'phone' => $phone,
                'error' => 'لم يتم العثور على عميل بهذا الرقم'
            ]);
        }

        // Get pending deliveries for this client
        $employee = $this->getCurrentEmployee();
        $employeeId = $employee->id ?? 0;
        
        // For staff users, show all pending deliveries, for employees show only their own
        if (Auth::user() && Auth::user()->isStaff()) {
            $deliveries = GiftDelivery::where('client_id', $client->id)
                                     ->where('status', 'pending')
                                     ->with(['gift', 'campaign'])
                                     ->get();
        } else {
            $deliveries = GiftDelivery::where('client_id', $client->id)
                                     ->where('employee_id', $employeeId)
                                     ->where('status', 'pending')
                                     ->with(['gift', 'campaign'])
                                     ->get();
        }

        if ($deliveries->isEmpty()) {
            return view('employee.search', [
                'phone' => $phone,
                'client' => $client,
                'error' => 'لا توجد هدايا معلقة للتسليم لهذا العميل'
            ]);
        }

        return view('employee.deliveries', compact('client', 'deliveries'));
    }

    /**
     * Confirm delivery of a gift
     */
    public function confirmDelivery(Request $request, GiftDelivery $delivery)
    {
        $employee = $this->getCurrentEmployee();

        // For staff users, allow confirming any delivery, for employees check ownership
        if (!Auth::user()->isStaff() && $employee->id && $delivery->employee_id !== $employee->id) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بتأكيد هذا التسليم'
            ], 403);
        }

        // Check if delivery is still pending
        if ($delivery->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'هذا التسليم تم بالفعل أو تم إلغاؤه'
            ]);
        }

        // Update delivery status
        $delivery->update([
            'status' => 'delivered',
            'delivered_at' => now(),
            'delivery_notes' => $request->notes ?? 'تم التسليم بنجاح'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تأكيد التسليم بنجاح'
        ]);
    }

    /**
     * Get delivery details
     */
    public function getDeliveryDetails(GiftDelivery $delivery)
    {
        $employee = $this->getCurrentEmployee();

        // For staff users, allow viewing any delivery, for employees check ownership
        if (!Auth::user()->isStaff() && $employee->id && $delivery->employee_id !== $employee->id) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بعرض هذا التسليم'
            ], 403);
        }

        $delivery->load(['client', 'gift', 'campaign']);

        return response()->json([
            'success' => true,
            'delivery' => [
                'id' => $delivery->id,
                'client_name' => $delivery->client->name,
                'client_phone' => $delivery->client->phone,
                'gift_name' => $delivery->gift->name,
                'gift_description' => $delivery->gift->description,
                'campaign_name' => $delivery->campaign->name ?? 'غير محدد',
                'status' => $delivery->status,
                'created_at' => $delivery->created_at->format('Y-m-d H:i'),
            ]
        ]);
    }

    /**
     * Employee statistics
     */
    public function statistics()
    {
        $employee = $this->getCurrentEmployee();
        $employeeId = $employee->id ?? 0;

        // For staff users, show overall statistics, for employees show their own
        if (Auth::user() && Auth::user()->isStaff()) {
            $stats = [
                'today' => GiftDelivery::where('status', 'delivered')
                                      ->whereDate('delivered_at', Carbon::today())
                                      ->count(),
                
                'this_week' => GiftDelivery::where('status', 'delivered')
                                         ->whereBetween('delivered_at', [
                                             Carbon::now()->startOfWeek(),
                                             Carbon::now()->endOfWeek()
                                         ])
                                         ->count(),
                
                'this_month' => GiftDelivery::where('status', 'delivered')
                                          ->whereMonth('delivered_at', Carbon::now()->month)
                                          ->whereYear('delivered_at', Carbon::now()->year)
                                          ->count(),
                
                'total' => GiftDelivery::where('status', 'delivered')->count(),
                'pending' => GiftDelivery::where('status', 'pending')->count()
            ];

            // Last 7 days chart data for all deliveries
            $chartData = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $count = GiftDelivery::where('status', 'delivered')
                                    ->whereDate('delivered_at', $date)
                                    ->count();
                
                $chartData[] = [
                    'date' => $date->format('m/d'),
                    'count' => $count
                ];
            }
        } else {
            $stats = [
                'today' => GiftDelivery::where('employee_id', $employeeId)
                                      ->where('status', 'delivered')
                                      ->whereDate('delivered_at', Carbon::today())
                                      ->count(),
                
                'this_week' => GiftDelivery::where('employee_id', $employeeId)
                                         ->where('status', 'delivered')
                                         ->whereBetween('delivered_at', [
                                             Carbon::now()->startOfWeek(),
                                             Carbon::now()->endOfWeek()
                                         ])
                                         ->count(),
                
                'this_month' => GiftDelivery::where('employee_id', $employeeId)
                                          ->where('status', 'delivered')
                                          ->whereMonth('delivered_at', Carbon::now()->month)
                                          ->whereYear('delivered_at', Carbon::now()->year)
                                          ->count(),
                
                'total' => GiftDelivery::where('employee_id', $employeeId)
                                     ->where('status', 'delivered')
                                     ->count(),

                'pending' => GiftDelivery::where('employee_id', $employeeId)
                                       ->where('status', 'pending')
                                       ->count()
            ];

            // Last 7 days chart data for this employee
            $chartData = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $count = GiftDelivery::where('employee_id', $employeeId)
                                    ->where('status', 'delivered')
                                    ->whereDate('delivered_at', $date)
                                    ->count();
                
                $chartData[] = [
                    'date' => $date->format('m/d'),
                    'count' => $count
                ];
            }
        }

        return view('employee.statistics', compact('stats', 'chartData'));
    }
} 