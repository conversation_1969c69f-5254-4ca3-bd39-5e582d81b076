@extends('layouts.admin')

@section('title', 'إدارة الحملات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-bullhorn me-2"></i>
                    إدارة الحملات الإعلانية
                </h1>
                <p class="page-subtitle">متابعة وإدارة جميع الحملات الإعلانية والترويجية</p>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item active" aria-current="page">الحملات الإعلانية</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card total">
                <div class="stat-icon">
                    <i class="fas fa-bullhorn"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ $stats['total'] }}</div>
                    <div class="stat-label">إجمالي الحملات</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card pending">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ $stats['pending_approval'] }}</div>
                    <div class="stat-label">في انتظار الموافقة</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card progress">
                <div class="stat-icon">
                    <i class="fas fa-play-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ $stats['in_progress'] }}</div>
                    <div class="stat-label">قيد التنفيذ</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card completed">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ $stats['completed'] }}</div>
                    <div class="stat-label">مكتملة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="action-bar">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="action-buttons">
                        <a href="{{ route('admin.campaigns.create') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء حملة جديدة
                        </a>
                        
                        <a href="{{ route('admin.campaigns.financial-reports') }}" class="btn btn-outline-info">
                            <i class="fas fa-chart-line me-2"></i>
                            التقارير المالية
                        </a>
                    </div>
                    
                    <div class="view-options">
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="view" id="grid-view" checked>
                            <label class="btn btn-outline-secondary" for="grid-view">
                                <i class="fas fa-th"></i>
                            </label>
                            
                            <input type="radio" class="btn-check" name="view" id="list-view">
                            <label class="btn btn-outline-secondary" for="list-view">
                                <i class="fas fa-list"></i>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaigns Grid -->
    <div class="row" id="campaigns-container">
        @forelse($campaigns as $campaign)
            <div class="col-lg-6 col-xl-4 mb-4 campaign-card">
                <div class="card campaign-item">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="campaign-info">
                                <h5 class="campaign-name">{{ $campaign->campaign_name ?? $campaign->gift->name }}</h5>
                                <p class="campaign-type">
                                    @switch($campaign->campaign_type)
                                        @case('promotional')
                                            <i class="fas fa-percent me-1"></i>ترويجية
                                            @break
                                        @case('seasonal')
                                            <i class="fas fa-calendar-alt me-1"></i>موسمية
                                            @break
                                        @case('loyalty')
                                            <i class="fas fa-heart me-1"></i>الولاء
                                            @break
                                        @case('birthday')
                                            <i class="fas fa-birthday-cake me-1"></i>عيد ميلاد
                                            @break
                                        @case('appreciation')
                                            <i class="fas fa-thumbs-up me-1"></i>تقدير وشكر
                                            @break
                                        @default
                                            <i class="fas fa-gift me-1"></i>عامة
                                    @endswitch
                                </p>
                                <p class="text-muted small">بواسطة: {{ $campaign->creator->name ?? 'الأدمن' }}</p>
                            </div>
                            
                            <div class="campaign-status">
                                @switch($campaign->stage)
                                    @case('gift_info')
                                        <span class="badge bg-info">معلومات الهدية</span>
                                        @break
                                    @case('template_selection')
                                        <span class="badge bg-warning">اختيار القالب</span>
                                        @break
                                    @case('client_filtering')
                                        <span class="badge bg-info">فلترة العملاء</span>
                                        @break
                                    @case('message_count')
                                        <span class="badge bg-primary">عدد الرسائل</span>
                                        @break
                                    @case('platform_selection')
                                        <span class="badge bg-secondary">اختيار المنصة</span>
                                        @break
                                    @case('payment')
                                        <span class="badge bg-warning">الدفع</span>
                                        @break
                                    @case('pending_approval')
                                        <span class="badge bg-warning">انتظار الموافقة</span>
                                        @break
                                    @case('approved')
                                        <span class="badge bg-success">معتمدة</span>
                                        @break
                                    @case('in_progress')
                                        <span class="badge bg-info">قيد التنفيذ</span>
                                        @break
                                    @case('completed')
                                        <span class="badge bg-success">مكتملة</span>
                                        @break
                                    @case('failed')
                                        <span class="badge bg-danger">فشلت</span>
                                        @break
                                    @default
                                        <span class="badge bg-light text-dark">جديدة</span>
                                @endswitch
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="campaign-details">
                            <div class="detail-item">
                                <span class="detail-label">
                                    <i class="fas fa-gift me-1"></i>
                                    الهدية:
                                </span>
                                <span class="detail-value">{{ $campaign->gift->name ?? 'غير محدد' }}</span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="detail-label">
                                    <i class="fas fa-users me-1"></i>
                                    العملاء المستهدفين:
                                </span>
                                <span class="detail-value">{{ $campaign->target_client_count ?? 0 }}</span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="detail-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    عدد الرسائل:
                                </span>
                                <span class="detail-value">{{ $campaign->message_count ?? 0 }}</span>
                            </div>
                            
                            <div class="detail-item">
                                <span class="detail-label">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    التكلفة:
                                </span>
                                <span class="detail-value">{{ number_format($campaign->total_cost ?? 0, 2) }} ريال</span>
                            </div>
                            
                            @if($campaign->stage == 'completed')
                                <div class="progress-section mt-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <small>معدل التسليم</small>
                                        <small>{{ $campaign->sent_count ?? 0 }}/{{ $campaign->message_count ?? 0 }}</small>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" 
                                             style="width: {{ $campaign->message_count > 0 ? (($campaign->sent_count ?? 0) / $campaign->message_count) * 100 : 0 }}%">
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="campaign-actions">
                            @if(in_array($campaign->stage, ['gift_info', 'template_selection', 'client_filtering', 'message_count', 'platform_selection', 'payment']))
                                <a href="{{ route('admin.campaigns.wizard', $campaign) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-arrow-right me-1"></i>
                                    متابعة الإنشاء
                                </a>
                            @endif
                            
                            @if($campaign->stage === 'pending_approval')
                                <button type="button" class="btn btn-success btn-sm" onclick="approveCampaign({{ $campaign->id }})">
                                    <i class="fas fa-check me-1"></i>
                                    موافقة
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="rejectCampaign({{ $campaign->id }})">
                                    <i class="fas fa-times me-1"></i>
                                    رفض
                                </button>
                            @elseif($campaign->stage === 'approved')
                                <button type="button" class="btn btn-info btn-sm" onclick="executeCampaign({{ $campaign->id }})">
                                    <i class="fas fa-play me-1"></i>
                                    تنفيذ
                                </button>
                            @endif
                            
                            @if($campaign->stage == 'completed')
                                <a href="{{ route('admin.campaigns.deliveries', $campaign) }}" class="btn btn-info btn-sm">
                                    <i class="fas fa-truck me-1"></i>
                                    التسليمات
                                </a>
                                
                                <a href="{{ route('admin.campaigns.analytics', $campaign) }}" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    التحليلات
                                </a>
                            @endif
                            
                            <a href="{{ route('admin.campaigns.show', $campaign) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>
                                عرض
                            </a>
                        </div>
                        
                        <div class="campaign-date">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $campaign->created_at->diffForHumans() }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <h3 class="empty-title">لا توجد حملات إعلانية حتى الآن</h3>
                    <p class="empty-description">ابدأ بإنشاء الحملة الأولى لاستهداف العملاء وتوزيع الهدايا</p>
                    <a href="{{ route('admin.campaigns.create') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء حملة جديدة
                    </a>
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($campaigns->hasPages())
        <div class="row">
            <div class="col-12">
                <div class="pagination-wrapper">
                    {{ $campaigns->links() }}
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض الحملة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">سبب الرفض</label>
                        <textarea name="admin_notes" class="form-control" rows="3" required 
                                placeholder="يرجى توضيح سبب رفض الحملة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">رفض الحملة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.page-header {
    margin-bottom: 2rem;
    text-align: center;
}

.page-title {
    color: #333;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stat-card.total {
    border-left-color: #667eea;
}

.stat-card.pending {
    border-left-color: #ffc107;
}

.stat-card.progress {
    border-left-color: #17a2b8;
}

.stat-card.completed {
    border-left-color: #28a745;
}

.stat-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
    opacity: 0.8;
}

.stat-card.total .stat-icon {
    color: #667eea;
}

.stat-card.pending .stat-icon {
    color: #ffc107;
}

.stat-card.progress .stat-icon {
    color: #17a2b8;
}

.stat-card.completed .stat-icon {
    color: #28a745;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
}

.action-bar {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.campaign-item {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.campaign-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.campaign-item .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px 15px 0 0;
}

.campaign-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: white;
}

.campaign-type {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 0.875rem;
}

.campaign-details {
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #f8f9fa;
}

.detail-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-label {
    color: #495057;
    font-weight: 500;
    flex: 1;
}

.detail-value {
    color: #667eea;
    font-weight: 600;
}

.campaign-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 0.75rem;
}

.campaign-date {
    border-top: 1px solid #f8f9fa;
    padding-top: 0.75rem;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    color: #e9ecef;
    margin-bottom: 1.5rem;
}

.empty-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-description {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

@media (max-width: 768px) {
    .campaign-actions {
        flex-direction: column;
    }
    
    .campaign-actions .btn {
        width: 100%;
    }
    
    .action-bar .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gridViewBtn = document.getElementById('grid-view');
    const listViewBtn = document.getElementById('list-view');
    const campaignsContainer = document.getElementById('campaigns-container');

    function toggleView() {
        if (listViewBtn.checked) {
            campaignsContainer.classList.add('list-view');
            document.querySelectorAll('.campaign-card').forEach(card => {
                card.className = 'col-12 mb-3 campaign-card';
            });
        } else {
            campaignsContainer.classList.remove('list-view');
            document.querySelectorAll('.campaign-card').forEach(card => {
                card.className = 'col-lg-6 col-xl-4 mb-4 campaign-card';
            });
        }
    }

    gridViewBtn.addEventListener('change', toggleView);
    listViewBtn.addEventListener('change', toggleView);
});
</script>

@endsection

@push('scripts')
<script>
let currentCampaignId = null;

function approveCampaign(campaignId) {
    if (confirm('هل أنت متأكد من اعتماد هذه الحملة؟')) {
        fetch(`/admin/campaigns/${campaignId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء اعتماد الحملة.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء اعتماد الحملة.');
        });
    }
}

function rejectCampaign(campaignId) {
    currentCampaignId = campaignId;
    document.getElementById('rejectForm').action = `/admin/campaigns/${campaignId}/reject`;
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

function executeCampaign(campaignId) {
    if (confirm('هل أنت متأكد من بدء تنفيذ هذه الحملة؟')) {
        fetch(`/admin/campaigns/${campaignId}/execute`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تنفيذ الحملة.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تنفيذ الحملة.');
        });
    }
}

function deleteCampaign(campaignId, campaignName) {
    if (confirm(`هل أنت متأكد من حذف الحملة "${campaignName}"?`)) {
        fetch(`/admin/campaigns/${campaignId}/delete`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف الحملة.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الحملة.');
        });
    }
}

document.getElementById('rejectForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء رفض الحملة.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء رفض الحملة.');
    });
});
</script>
@endpush 