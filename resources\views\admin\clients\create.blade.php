@extends('layouts.admin')

@section('title', 'إضافة عميل جديد')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة عميل جديد
                        </h4>
                        <a href="{{ route('admin.clients.index') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>
                            العودة
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <form action="{{ route('admin.clients.store') }}" method="POST" id="clientForm">
                        @csrf
                        
                        <div class="row">
                            <!-- Personal Information -->
                            <div class="col-lg-6">
                                <div class="card border-0 shadow-sm mb-4">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">
                                            <i class="fas fa-user me-2"></i>
                                            المعلومات الشخصية
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="name" class="form-label required">الاسم الكامل</label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name') }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                       id="email" name="email" value="{{ old('email') }}">
                                                @error('email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="phone" class="form-label required">رقم الهاتف</label>
                                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                                       id="phone" name="phone" value="{{ old('phone') }}" required>
                                                @error('phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="gender" class="form-label required">الجنس</label>
                                                <select class="form-select @error('gender') is-invalid @enderror" 
                                                        id="gender" name="gender" required>
                                                    <option value="">اختر الجنس</option>
                                                    <option value="ذكر" {{ old('gender') == 'ذكر' ? 'selected' : '' }}>ذكر</option>
                                                    <option value="أنثى" {{ old('gender') == 'أنثى' ? 'selected' : '' }}>أنثى</option>
                                                </select>
                                                @error('gender')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="age" class="form-label required">العمر</label>
                                                <input type="number" class="form-control @error('age') is-invalid @enderror" 
                                                       id="age" name="age" value="{{ old('age') }}" min="1" max="120" required>
                                                @error('age')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="birthdate" class="form-label">تاريخ الميلاد</label>
                                            <input type="date" class="form-control @error('birthdate') is-invalid @enderror" 
                                                   id="birthdate" name="birthdate" value="{{ old('birthdate') }}">
                                            @error('birthdate')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="occupation" class="form-label">المهنة</label>
                                            <input type="text" class="form-control @error('occupation') is-invalid @enderror" 
                                                   id="occupation" name="occupation" value="{{ old('occupation') }}">
                                            @error('occupation')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="health_condition" class="form-label">الحالة الصحية</label>
                                            <input type="text" class="form-control @error('health_condition') is-invalid @enderror" 
                                                   id="health_condition" name="health_condition" value="{{ old('health_condition') }}">
                                            @error('health_condition')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Address Information -->
                            <div class="col-lg-6">
                                <div class="card border-0 shadow-sm mb-4">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">
                                            <i class="fas fa-map-marker-alt me-2"></i>
                                            معلومات العنوان
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="country" class="form-label required">الدولة</label>
                                            <input type="text" class="form-control @error('country') is-invalid @enderror" 
                                                   id="country" name="country" value="{{ old('country', 'السعودية') }}" required>
                                            @error('country')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="city" class="form-label required">المدينة</label>
                                            <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                                   id="city" name="city" value="{{ old('city') }}" required>
                                            @error('city')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="neighborhood" class="form-label">الحي</label>
                                            <input type="text" class="form-control @error('neighborhood') is-invalid @enderror" 
                                                   id="neighborhood" name="neighborhood" value="{{ old('neighborhood') }}">
                                            @error('neighborhood')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Payment Information -->
                                <div class="card border-0 shadow-sm mb-4">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">
                                            <i class="fas fa-credit-card me-2"></i>
                                            معلومات الدفع
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="amount_paid" class="form-label">المبلغ المدفوع</label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control @error('amount_paid') is-invalid @enderror" 
                                                           id="amount_paid" name="amount_paid" value="{{ old('amount_paid', 0) }}" 
                                                           step="0.01" min="0">
                                                    <span class="input-group-text">ريال</span>
                                                </div>
                                                @error('amount_paid')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <label for="payment_status" class="form-label">حالة الدفع</label>
                                                <select class="form-select @error('payment_status') is-invalid @enderror" 
                                                        id="payment_status" name="payment_status">
                                                    <option value="pending" {{ old('payment_status') == 'pending' ? 'selected' : '' }}>في الانتظار</option>
                                                    <option value="paid" {{ old('payment_status') == 'paid' ? 'selected' : '' }}>مدفوع</option>
                                                    <option value="failed" {{ old('payment_status') == 'failed' ? 'selected' : '' }}>فشل</option>
                                                    <option value="refunded" {{ old('payment_status') == 'refunded' ? 'selected' : '' }}>مسترد</option>
                                                </select>
                                                @error('payment_status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="payment_method" class="form-label">طريقة الدفع</label>
                                            <input type="text" class="form-control @error('payment_method') is-invalid @enderror" 
                                                   id="payment_method" name="payment_method" value="{{ old('payment_method') }}">
                                            @error('payment_method')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="assigned_employee" class="form-label">الموظف المخصص</label>
                                            <input type="text" class="form-control @error('assigned_employee') is-invalid @enderror" 
                                                   id="assigned_employee" name="assigned_employee" value="{{ old('assigned_employee') }}">
                                            @error('assigned_employee')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Campaign & Gift Information -->
                                <div class="card border-0 shadow-sm mb-4">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">
                                            <i class="fas fa-gift me-2"></i>
                                            معلومات الهدية والحملة
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="gift_id" class="form-label">الهدية</label>
                                            <select class="form-select @error('gift_id') is-invalid @enderror" 
                                                    id="gift_id" name="gift_id">
                                                <option value="">اختر الهدية</option>
                                                @foreach($gifts ?? [] as $gift)
                                                    <option value="{{ $gift->id }}" {{ old('gift_id') == $gift->id ? 'selected' : '' }}>
                                                        {{ $gift->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('gift_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="campaign_id" class="form-label">الحملة</label>
                                            <select class="form-select @error('campaign_id') is-invalid @enderror" 
                                                    id="campaign_id" name="campaign_id">
                                                <option value="">اختر الحملة</option>
                                                @foreach($campaigns ?? [] as $campaign)
                                                    <option value="{{ $campaign->id }}" {{ old('campaign_id') == $campaign->id ? 'selected' : '' }}>
                                                        {{ $campaign->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('campaign_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="message_received" name="message_received" value="1" 
                                                           {{ old('message_received') ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="message_received">
                                                        تم استلام الرسالة
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="col-md-6 mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="gift_received" name="gift_received" value="1" 
                                                           {{ old('gift_received') ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="gift_received">
                                                        تم استلام الهدية
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="rating" class="form-label">التقييم</label>
                                                <select class="form-select @error('rating') is-invalid @enderror" 
                                                        id="rating" name="rating">
                                                    <option value="">اختر التقييم</option>
                                                    <option value="1" {{ old('rating') == '1' ? 'selected' : '' }}>⭐ 1</option>
                                                    <option value="2" {{ old('rating') == '2' ? 'selected' : '' }}>⭐ 2</option>
                                                    <option value="3" {{ old('rating') == '3' ? 'selected' : '' }}>⭐ 3</option>
                                                    <option value="4" {{ old('rating') == '4' ? 'selected' : '' }}>⭐ 4</option>
                                                    <option value="5" {{ old('rating') == '5' ? 'selected' : '' }}>⭐ 5</option>
                                                </select>
                                                @error('rating')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="feedback" class="form-label">التعليق</label>
                                            <textarea class="form-control @error('feedback') is-invalid @enderror" 
                                                      id="feedback" name="feedback" rows="3">{{ old('feedback') }}</textarea>
                                            @error('feedback')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if(!empty($dynamicFields))
                            <!-- Dynamic Fields -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="card border-0 shadow-sm mb-4">
                                        <div class="card-header bg-warning text-dark">
                                            <h5 class="mb-0">
                                                <i class="fas fa-plus-square me-2"></i>
                                                الحقول الديناميكية
                                                <small class="ms-2">(تم إنشاؤها من استيراد إكسل)</small>
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                @foreach($dynamicFields as $index => $field)
                                                    <div class="col-md-6 mb-3">
                                                        <label for="dynamic_{{ $field }}" class="form-label">
                                                            {{ $field }}
                                                            <small class="text-muted">(اختياري)</small>
                                                        </label>
                                                        <input type="text" class="form-control" 
                                                               id="dynamic_{{ $field }}" 
                                                               name="custom_{{ $field }}" 
                                                               value="{{ old('custom_' . $field) }}"
                                                               placeholder="أدخل {{ $field }}">
                                                    </div>
                                                    @if(($index + 1) % 2 == 0)
                                                        </div><div class="row">
                                                    @endif
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.clients.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ العميل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.required::after {
    content: " *";
    color: #dc3545;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-calculate age from birthdate
    document.getElementById('birthdate').addEventListener('change', function() {
        const birthdate = new Date(this.value);
        const today = new Date();
        let age = today.getFullYear() - birthdate.getFullYear();
        const monthDiff = today.getMonth() - birthdate.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthdate.getDate())) {
            age--;
        }
        
        if (age >= 0 && age <= 120) {
            document.getElementById('age').value = age;
        }
    });

    // Form validation
    document.getElementById('clientForm').addEventListener('submit', function(e) {
        const required = this.querySelectorAll('[required]');
        let valid = true;

        required.forEach(function(field) {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                valid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!valid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
});
</script>
@endsection 