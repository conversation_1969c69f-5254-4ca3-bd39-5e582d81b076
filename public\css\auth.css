/* Import professional fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&family=Poppins:wght@400;500;600;700;800&display=swap');
@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css");

:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #00d4aa;
  --warning-color: #ffc107;
  --danger-color: #ff4757;
  --info-color: #3867d6;
  --dark-bg: #1a1a1a;
  --card-bg: #2d2d2d;
  --text-light: #666666;
  --text-white: #ffffff;
  --text-dark: #333333;
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.1);
  --dark-color: #1a0033;
  --light-color: #ffffff;
  --text-color: #333;
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
  --font-heading: 'Poppins', 'Cairo', sans-serif;
  --font-body: 'Roboto', 'Cairo', sans-serif;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
  color: var(--text-white);
  min-height: 100vh;
  overflow-x: hidden;
  direction: rtl;
}

[dir="ltr"] body {
  direction: ltr;
}

/* RTL Support */
.rtl {
  direction: rtl;
  text-align: right;
  font-family: 'Cairo', var(--font-body);
}

.ltr {
  direction: ltr;
  text-align: left;
  font-family: var(--font-body);
}

/* Better Arabic Text Rendering */
.rtl * {
  font-family: 'Cairo', var(--font-body);
}

.ltr * {
  font-family: var(--font-body);
}

/* Global Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Animated Background */
.animated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
  z-index: -2;
}

.animated-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(106, 27, 154, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 107, 53, 0.2) 0%, transparent 50%);
  animation: backgroundShift 15s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(180deg); }
}

/* Floating Elements */
.floating-elements {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.floating-shape {
  position: absolute;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(106, 27, 154, 0.1));
  border-radius: 50%;
  filter: blur(3px);
}

.floating-shape:nth-child(1) {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation: float 6s ease-in-out infinite;
}

.floating-shape:nth-child(2) {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 10%;
  animation: float 8s ease-in-out infinite reverse;
}

.floating-shape:nth-child(3) {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation: float 7s ease-in-out infinite;
}

.floating-shape:nth-child(4) {
  width: 100px;
  height: 100px;
  bottom: 20%;
  right: 30%;
  animation: float 9s ease-in-out infinite reverse;
}

/* Login/Register Form Container */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

.auth-card {
  background: linear-gradient(145deg, rgba(45, 45, 45, 0.9), rgba(26, 26, 26, 0.9));
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 25px;
  padding: 40px;
  width: 100%;
  max-width: 450px;
  box-shadow: 0 25px 50px var(--shadow-color);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out;
}

.auth-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: 25px 25px 0 0;
}

/* Logo and Header */
.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  box-shadow: 0 10px 25px rgba(255, 107, 53, 0.3);
}

.auth-title {
  font-size: 1.8rem;
  font-weight: 800;
  margin-bottom: 10px;
  background: linear-gradient(135deg, var(--text-white) 0%, var(--primary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-subtitle {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-bottom: 20px;
}

/* Language Toggle */
.language-toggle {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.3);
  border-radius: 12px;
  padding: 8px 15px;
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.language-toggle:hover {
  background: rgba(255, 107, 53, 0.2);
  color: white;
  transform: translateY(-2px);
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
  position: relative;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-light);
  font-weight: 600;
  font-size: 0.9rem;
}

.form-control {
  width: 100%;
  padding: 15px;
  padding-right: 45px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-white);
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

[dir="ltr"] .form-control {
  padding-left: 45px;
  padding-right: 15px;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 107, 53, 0.1);
  box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Input Icons */
.input-icon {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  color: var(--primary-color);
  font-size: 1.1rem;
  pointer-events: none;
}

[dir="ltr"] .input-icon {
  left: 15px;
  right: auto;
}

/* Select Dropdown */
.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ff6b35' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left 12px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-left: 40px;
}

[dir="ltr"] .form-select {
  background-position: right 12px center;
  padding-right: 40px;
  padding-left: 15px;
}

/* Checkbox Styles */
.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.form-check-input {
  width: 18px;
  height: 18px;
  margin-left: 10px;
  accent-color: var(--primary-color);
}

[dir="ltr"] .form-check-input {
  margin-right: 10px;
  margin-left: 0;
}

.form-check-label {
  color: var(--text-light);
  font-size: 0.9rem;
  cursor: pointer;
}

/* Button Styles */
.btn-primary {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 15px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(255, 107, 53, 0.4);
}

/* Links */
.auth-links {
  text-align: center;
  margin-top: 25px;
}

.auth-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.auth-link:hover {
  color: white;
  text-shadow: 0 0 10px var(--primary-color);
}

/* Error Messages */
.invalid-feedback {
  color: var(--danger-color);
  font-size: 0.85rem;
  margin-top: 5px;
  display: block;
}

.alert {
  padding: 12px 18px;
  border-radius: 12px;
  margin-bottom: 20px;
  border: none;
}

.alert-danger {
  background: rgba(255, 71, 87, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(255, 71, 87, 0.3);
}

.alert-success {
  background: rgba(0, 212, 170, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(0, 212, 170, 0.3);
}

/* Loading State */
.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    padding: 15px;
  }
  
  .auth-card {
    padding: 30px 25px;
    border-radius: 20px;
  }
  
  .auth-title {
    font-size: 1.5rem;
  }
  
  .floating-shape {
    display: none;
  }
}

@media (max-width: 480px) {
  .auth-card {
    padding: 25px 20px;
  }
  
  .form-control {
    padding: 12px 40px 12px 15px;
  }
  
  [dir="ltr"] .form-control {
    padding: 12px 15px 12px 40px;
  }
}

/* Admin Layout Enhancements */
.admin-layout {
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.sidebar {
  background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
  border-right: 1px solid var(--border-color);
}

.sidebar-link {
  color: var(--text-light);
  transition: all 0.3s ease;
}

.sidebar-link:hover,
.sidebar-link.active {
  color: var(--primary-color);
  background: rgba(255, 107, 53, 0.1);
}

.main-content {
  background: transparent;
  padding: 30px;
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px var(--shadow-color);
}

/* Animation Classes */
.fade-in {
  animation: fadeInUp 0.6s ease-out;
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Login Container */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* Animated Background Shapes */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 20%;
  right: 20%;
  animation-delay: 2s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  bottom: 30%;
  left: 30%;
  animation-delay: 4s;
}

.shape-4 {
  width: 120px;
  height: 120px;
  bottom: 20%;
  right: 10%;
  animation-delay: 1s;
}

/* Language Switcher */
.language-switcher {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.lang-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 20px;
  margin: 0 5px;
  font-size: 14px;
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

.lang-btn.active,
.lang-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  text-decoration: none;
}

/* Login Card */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 450px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  opacity: 0;
  transform: translateY(30px);
  transition: var(--transition);
}

.login-card.loaded {
  opacity: 1;
  transform: translateY(0);
}

.login-card-inner {
  position: relative;
}

/* Logo */
.login-logo {
  text-align: center;
  margin-bottom: 30px;
}

.logo-text {
  font-size: 2.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

/* Title */
.login-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  text-align: center;
  margin-bottom: 10px;
}

.login-subtitle {
  color: var(--text-light);
  text-align: center;
  margin-bottom: 30px;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Form Styles */
.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-dark);
  font-weight: 600;
  font-size: 0.9rem;
}

.input-with-icon {
  position: relative;
}

.input-with-icon i {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: 1.1rem;
  z-index: 5;
}

.rtl .input-with-icon i {
  right: 15px;
}

.ltr .input-with-icon i {
  left: 15px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 1rem;
  transition: var(--transition);
  background: #fafafa;
  color: var(--text-dark);
}

.rtl .form-group input,
.rtl .form-group select {
  padding-right: 50px;
}

.ltr .form-group input,
.ltr .form-group select {
  padding-left: 50px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input::placeholder {
  color: #999;
}

.role-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
}

.rtl .role-select {
  background-position: right 12px center;
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 10px;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
}

.remember-me input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.remember-me label {
  margin: 0;
  color: var(--text-light);
  font-size: 0.9rem;
  cursor: pointer;
}

.forgot-password {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
  transition: var(--transition);
}

.forgot-password:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

/* Login Button */
.login-button {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

/* Footer Links */
.login-footer {
  text-align: center;
  margin-top: 20px;
  color: var(--text-light);
  font-size: 0.9rem;
}

.register-link,
.back-to-home {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

.register-link:hover,
.back-to-home:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

.back-to-home {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-top: 15px;
}

.rtl-flip {
  transform: scaleX(-1);
}

.rtl .rtl-flip {
  transform: scaleX(1);
}

/* Messages */
.success-message,
.error-message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.success-message {
  background: rgba(0, 212, 170, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(0, 212, 170, 0.2);
}

.error-message {
  background: rgba(255, 71, 87, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(255, 71, 87, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    padding: 15px;
  }
  
  .login-card {
    padding: 30px 25px;
    max-width: 100%;
  }
  
  .logo-text {
    font-size: 2rem;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
  
  .language-switcher {
    position: static;
    text-align: center;
    margin-bottom: 20px;
  }
  
  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 25px 20px;
  }
  
  .form-group input,
  .form-group select {
    padding: 12px 15px;
  }
  
  .rtl .form-group input,
  .rtl .form-group select {
    padding-right: 45px;
  }
  
  .ltr .form-group input,
  .ltr .form-group select {
    padding-left: 45px;
  }
}

/* Animation on load */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-card {
  animation: slideInUp 0.6s ease-out;
} 