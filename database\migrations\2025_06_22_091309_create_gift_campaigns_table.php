<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gift_campaigns', function (Blueprint $table) {
            $table->id();
            $table->foreignId('gift_id')->constrained()->onDelete('cascade');
            $table->foreignId('message_template_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            
            // Campaign stages
            $table->enum('stage', ['gift_info', 'template_selection', 'client_filtering', 'message_count', 'platform_selection', 'payment', 'pending_approval', 'approved', 'processing', 'completed', 'failed'])->default('gift_info');
            
            // Template selection
            $table->enum('template_type', ['prebuilt', 'custom'])->nullable();
            $table->text('custom_message')->nullable();
            $table->string('custom_media_type')->nullable(); // audio, video, image
            $table->string('custom_media_path')->nullable();
            
            // Client filters (stored as JSON)
            $table->json('client_filters')->nullable();
            $table->integer('target_client_count')->default(0);
            $table->integer('message_count')->default(0);
            
            // Platform selection
            $table->enum('platform', ['own', 'external'])->nullable();
            $table->string('whatsapp_token')->nullable();
            $table->enum('messaging_channel', ['whatsapp', 'email', 'sms'])->default('whatsapp');
            
            // Payment & Status
            $table->decimal('total_cost', 10, 2)->default(0);
            $table->enum('payment_status', ['pending', 'paid', 'failed'])->default('pending');
            $table->string('payment_id')->nullable();
            
            // Execution tracking
            $table->integer('sent_count')->default(0);
            $table->integer('failed_count')->default(0);
            $table->datetime('started_at')->nullable();
            $table->datetime('completed_at')->nullable();
            
            // Admin approval
            $table->boolean('requires_approval')->default(false);
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->datetime('approved_at')->nullable();
            $table->text('admin_notes')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gift_campaigns');
    }
};
