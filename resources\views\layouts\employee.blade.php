<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'لوحة الموظف') - هدايا السعودية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --employee-primary: #28a745;
            --employee-secondary: #17a2b8;
            --employee-accent: #ffc107;
            --employee-danger: #dc3545;
            --employee-dark: #343a40;
            --employee-light: #f8f9fa;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--employee-primary) 0%, #20c997 100%);
            min-height: 100vh;
            padding: 0;
            margin: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Mobile-First Container */
        .mobile-container {
            max-width: 100%;
            min-height: 100vh;
            background: #fff;
            position: relative;
            overflow-x: hidden;
        }

        /* Top Header */
        .mobile-header {
            background: linear-gradient(135deg, var(--employee-primary) 0%, #20c997 100%);
            color: white;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        .header-user {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .header-user img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        /* Content Area */
        .mobile-content {
            padding: 1rem;
            padding-bottom: 80px; /* Space for bottom nav */
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e9ecef;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .nav-tabs {
            display: flex;
            border: none;
            margin: 0;
        }

        .nav-item {
            flex: 1;
        }

        .nav-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.75rem 0.5rem;
            color: #6c757d;
            text-decoration: none;
            border: none;
            border-radius: 0;
            font-size: 0.75rem;
            transition: all 0.3s ease;
        }

        .nav-link i {
            font-size: 1.25rem;
            margin-bottom: 0.25rem;
        }

        .nav-link.active,
        .nav-link:hover {
            color: var(--employee-primary);
            background: rgba(40, 167, 69, 0.1);
        }

        /* Cards */
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--employee-primary);
            margin-bottom: 1rem;
        }

        .stat-card.warning {
            border-left-color: var(--employee-accent);
        }

        .stat-card.info {
            border-left-color: var(--employee-secondary);
        }

        .stat-card.danger {
            border-left-color: var(--employee-danger);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--employee-primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        /* Search Box */
        .search-container {
            position: relative;
            margin-bottom: 1rem;
        }

        .search-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--employee-primary);
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.1rem;
        }

        /* Buttons */
        .btn-mobile {
            width: 100%;
            padding: 1rem;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 10px;
            border: none;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
        }

        .btn-success-mobile {
            background: var(--employee-primary);
            color: white;
        }

        .btn-success-mobile:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-warning-mobile {
            background: var(--employee-accent);
            color: var(--employee-dark);
        }

        .btn-info-mobile {
            background: var(--employee-secondary);
            color: white;
        }

        /* Delivery Card */
        .delivery-card {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }

        .delivery-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .delivery-title {
            font-weight: 600;
            color: var(--employee-dark);
            margin: 0;
        }

        .delivery-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.2);
            color: #856404;
        }

        .status-delivered {
            background: rgba(40, 167, 69, 0.2);
            color: #155724;
        }

        /* Alert Messages */
        .alert-mobile {
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: none;
        }

        .alert-success-mobile {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border-left: 4px solid var(--employee-primary);
        }

        .alert-danger-mobile {
            background: rgba(220, 53, 69, 0.1);
            color: #721c24;
            border-left: 4px solid var(--employee-danger);
        }

        /* Loading Spinner */
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .spinner-border {
            color: var(--employee-primary);
        }

        /* Success Animation */
        .success-animation {
            text-align: center;
            padding: 2rem;
        }

        .success-checkmark {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--employee-primary);
            color: white;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Responsive Adjustments */
        @media (max-width: 576px) {
            .mobile-content {
                padding: 0.75rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .delivery-card {
                padding: 0.75rem;
            }

            .header-title {
                font-size: 1.1rem;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .mobile-container {
                background: #1a1a1a;
                color: #e9ecef;
            }

            .stat-card, .delivery-card {
                background: #2d3748;
                color: #e9ecef;
            }

            .bottom-nav {
                background: #2d3748;
                border-color: #4a5568;
            }
        }
    </style>

    @stack('styles')
</head>
<body>
    <div class="mobile-container">
        <!-- Top Header -->
        <header class="mobile-header">
            <div class="header-content">
                <h1 class="header-title">@yield('page-title', 'لوحة الموظف')</h1>
                @if(Auth::guard('employee')->check() || Auth::check())
                <div class="header-user">
                    <i class="fas fa-user-circle"></i>
                    <span>{{ Auth::guard('employee')->user()->name ?? Auth::user()->name ?? 'الموظف' }}</span>
                </div>
                @endif
            </div>
        </header>

        <!-- Content Area -->
        <main class="mobile-content">
            @if(session('success'))
                <div class="alert-mobile alert-success-mobile">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert-mobile alert-danger-mobile">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    {{ session('error') }}
                </div>
            @endif

            @if($errors->any())
                <div class="alert-mobile alert-danger-mobile">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    @foreach($errors->all() as $error)
                        <div>{{ $error }}</div>
                    @endforeach
                </div>
            @endif

            @yield('content')
        </main>

        <!-- Bottom Navigation -->
        @if(Auth::guard('employee')->check() || Auth::check())
        <nav class="bottom-nav">
            <ul class="nav-tabs">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('employee.dashboard') ? 'active' : '' }}" 
                       href="{{ route('employee.dashboard') }}">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('employee.search*') ? 'active' : '' }}" 
                       href="{{ route('employee.search') }}">
                        <i class="fas fa-search"></i>
                        بحث
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('employee.statistics') ? 'active' : '' }}" 
                       href="{{ route('employee.statistics') }}">
                        <i class="fas fa-chart-bar"></i>
                        إحصائيات
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        خروج
                    </a>
                </li>
            </ul>
        </nav>
        @endif
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // CSRF Token for AJAX requests
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Logout function
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                
                // Check which guard is being used and set appropriate logout route
                @if(Auth::guard('employee')->check())
                    form.action = '{{ route("employee.logout") }}';
                @else
                    form.action = '{{ route("logout") }}';
                @endif
                
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_token';
                csrfInput.value = csrfToken;
                
                form.appendChild(csrfInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Show loading spinner
        function showLoading() {
            const spinner = document.querySelector('.loading-spinner');
            if (spinner) {
                spinner.style.display = 'block';
            }
        }

        // Hide loading spinner
        function hideLoading() {
            const spinner = document.querySelector('.loading-spinner');
            if (spinner) {
                spinner.style.display = 'none';
            }
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert-mobile');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            });
        });
    </script>

    @stack('scripts')
</body>
</html> 