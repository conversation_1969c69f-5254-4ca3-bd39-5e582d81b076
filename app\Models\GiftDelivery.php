<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class GiftDelivery extends Model
{
    use HasFactory;

    protected $fillable = [
        'campaign_id',
        'client_id',
        'gift_id',
        'employee_id',
        'vendor_id',
        'delivery_code',
        'status',
        'delivered_at',
        'delivery_notes',
        'delivery_address',
        'delivery_latitude',
        'delivery_longitude',
        'client_signature',
        'delivery_photo',
        'client_rating',
        'client_feedback',
        'client_satisfied',
        'tracking_number',
        'expected_delivery_date',
        'delivery_attempts',
        'delivery_failure_reason',
        'communication_log',
        'last_contact_at'
    ];

    protected $casts = [
        'delivered_at' => 'datetime',
        'expected_delivery_date' => 'datetime',
        'last_contact_at' => 'datetime',
        'communication_log' => 'array',
        'client_satisfied' => 'boolean',
        'delivery_attempts' => 'integer',
        'client_rating' => 'integer',
        'delivery_latitude' => 'decimal:8',
        'delivery_longitude' => 'decimal:8'
    ];

    // Constants
    const STATUS_PENDING = 'pending';
    const STATUS_DELIVERED = 'delivered';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_EXPIRED = 'expired';

    // Relationships
    public function campaign()
    {
        return $this->belongsTo(GiftCampaign::class);
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function gift()
    {
        return $this->belongsTo(Gift::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeDelivered($query)
    {
        return $query->where('status', self::STATUS_DELIVERED);
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', self::STATUS_CANCELLED);
    }

    public function scopeExpired($query)
    {
        return $query->where('status', self::STATUS_EXPIRED);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('delivered_at', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('delivered_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('delivered_at', now()->month)
                    ->whereYear('delivered_at', now()->year);
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', self::STATUS_PENDING)
                    ->where('expected_delivery_date', '<', now());
    }

    // Accessors
    public function getStatusDisplayNameAttribute()
    {
        $names = [
            self::STATUS_PENDING => 'في الانتظار',
            self::STATUS_DELIVERED => 'تم التسليم',
            self::STATUS_CANCELLED => 'ملغي',
            self::STATUS_EXPIRED => 'منتهي الصلاحية'
        ];

        return $names[$this->status] ?? 'غير محدد';
    }

    public function getStatusBadgeClassAttribute()
    {
        $classes = [
            self::STATUS_PENDING => 'badge-warning',
            self::STATUS_DELIVERED => 'badge-success',
            self::STATUS_CANCELLED => 'badge-danger',
            self::STATUS_EXPIRED => 'badge-secondary'
        ];

        return $classes[$this->status] ?? 'badge-secondary';
    }

    public function getClientRatingStarsAttribute()
    {
        if (!$this->client_rating) return '';
        
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            $stars .= $i <= $this->client_rating ? '★' : '☆';
        }
        return $stars;
    }

    public function getDeliveryStatusColorAttribute()
    {
        $colors = [
            self::STATUS_PENDING => '#ffc107',
            self::STATUS_DELIVERED => '#28a745',
            self::STATUS_CANCELLED => '#dc3545',
            self::STATUS_EXPIRED => '#6c757d'
        ];

        return $colors[$this->status] ?? '#6c757d';
    }

    public function getIsOverdueAttribute()
    {
        return $this->status === self::STATUS_PENDING && 
               $this->expected_delivery_date && 
               $this->expected_delivery_date->isPast();
    }

    public function getDaysUntilDeliveryAttribute()
    {
        if (!$this->expected_delivery_date) return null;
        
        return now()->diffInDays($this->expected_delivery_date, false);
    }

    public function getDeliveryPhotoUrlAttribute()
    {
        return $this->delivery_photo 
            ? asset('storage/' . $this->delivery_photo)
            : null;
    }

    public function getClientSignatureUrlAttribute()
    {
        return $this->client_signature 
            ? asset('storage/' . $this->client_signature)
            : null;
    }

    // Methods
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    public function isDelivered()
    {
        return $this->status === self::STATUS_DELIVERED;
    }

    public function isCancelled()
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    public function isExpired()
    {
        return $this->status === self::STATUS_EXPIRED;
    }

    public function canBeDelivered()
    {
        return $this->isPending() && !$this->isOverdue;
    }

    public function markAsDelivered($employeeId = null, $notes = null, $location = null)
    {
        $this->update([
            'status' => self::STATUS_DELIVERED,
            'delivered_at' => now(),
            'delivery_notes' => $notes,
            'employee_id' => $employeeId ?: $this->employee_id,
            'delivery_latitude' => $location['latitude'] ?? null,
            'delivery_longitude' => $location['longitude'] ?? null
        ]);

        // Update employee performance
        if ($this->employee) {
            $this->employee->updatePerformanceRating();
        }

        // Log communication
        $this->logCommunication('delivery_completed', 'تم تسليم الهدية بنجاح');
    }

    public function markAsCancelled($reason = null)
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'delivery_failure_reason' => $reason
        ]);

        $this->logCommunication('delivery_cancelled', $reason);
    }

    public function incrementDeliveryAttempt($reason = null)
    {
        $this->increment('delivery_attempts');
        
        if ($reason) {
            $this->update(['delivery_failure_reason' => $reason]);
        }

        $this->logCommunication('delivery_attempt', "محاولة تسليم #{$this->delivery_attempts}: " . $reason);

        // Auto-cancel after 3 failed attempts
        if ($this->delivery_attempts >= 3) {
            $this->markAsCancelled('تم إلغاء التسليم بعد 3 محاولات فاشلة');
        }
    }

    public function logCommunication($type, $message, $metadata = [])
    {
        $log = $this->communication_log ?? [];
        
        $log[] = [
            'type' => $type,
            'message' => $message,
            'timestamp' => now()->toISOString(),
            'metadata' => $metadata
        ];

        $this->update([
            'communication_log' => $log,
            'last_contact_at' => now()
        ]);
    }

    public function generateTrackingNumber()
    {
        $prefix = 'TRK';
        $vendorCode = strtoupper(substr($this->vendor->business_name ?? 'VEN', 0, 3));
        $date = now()->format('Ymd');
        $random = str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        return $prefix . $vendorCode . $date . $random;
    }

    public function generateDeliveryCode()
    {
        $prefix = 'DEL';
        $campaignId = str_pad($this->campaign_id, 4, '0', STR_PAD_LEFT);
        $clientId = str_pad($this->client_id, 6, '0', STR_PAD_LEFT);
        $random = str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
        
        return $prefix . $campaignId . $clientId . $random;
    }

    public function getQrCodeData()
    {
        return [
            'delivery_code' => $this->delivery_code,
            'tracking_number' => $this->tracking_number,
            'client_name' => $this->client->name,
            'gift_name' => $this->gift->name,
            'vendor_name' => $this->vendor->business_name,
            'campaign_name' => $this->campaign->name,
            'verification_url' => route('delivery.verify', $this->delivery_code)
        ];
    }

    // Boot method to auto-generate codes
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($delivery) {
            if (empty($delivery->tracking_number)) {
                $delivery->tracking_number = $delivery->generateTrackingNumber();
            }
            
            if (empty($delivery->delivery_code)) {
                $delivery->delivery_code = $delivery->generateDeliveryCode();
            }
        });
    }
}
