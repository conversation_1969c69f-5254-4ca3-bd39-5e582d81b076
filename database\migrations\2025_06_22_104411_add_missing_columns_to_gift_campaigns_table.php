<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gift_campaigns', function (Blueprint $table) {
            // Add missing foreign keys
            $table->foreignId('gift_id')->constrained()->onDelete('cascade');
            $table->foreignId('message_template_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            
            // Campaign stages
            $table->enum('stage', ['gift_info', 'template_selection', 'client_filtering', 'message_count', 'platform_selection', 'payment', 'pending_approval', 'approved', 'processing', 'completed', 'failed'])->default('gift_info');
            
            // Template selection
            $table->enum('template_type', ['prebuilt', 'custom'])->nullable();
            $table->text('custom_message')->nullable();
            $table->string('custom_media_type')->nullable(); // audio, video, image
            $table->string('custom_media_path')->nullable();
            
            // Client filters (stored as JSON)
            $table->json('client_filters')->nullable();
            $table->integer('target_client_count')->default(0);
            $table->integer('message_count')->default(0);
            
            // Platform selection
            $table->enum('platform', ['own', 'external'])->nullable();
            $table->string('whatsapp_token')->nullable();
            $table->enum('messaging_channel', ['whatsapp', 'email', 'sms'])->default('whatsapp');
            
            // Payment & Status
            $table->decimal('total_cost', 10, 2)->default(0);
            $table->enum('payment_status', ['pending', 'paid', 'failed'])->default('pending');
            $table->string('payment_id')->nullable();
            
            // Execution tracking
            $table->integer('sent_count')->default(0);
            $table->integer('failed_count')->default(0);
            $table->datetime('started_at')->nullable();
            $table->datetime('completed_at')->nullable();
            
            // Admin approval
            $table->boolean('requires_approval')->default(false);
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->datetime('approved_at')->nullable();
            $table->text('admin_notes')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gift_campaigns', function (Blueprint $table) {
            // Drop foreign key constraints first
            $table->dropForeign(['gift_id']);
            $table->dropForeign(['message_template_id']);
            $table->dropForeign(['created_by']);
            $table->dropForeign(['approved_by']);
            
            // Drop columns
            $table->dropColumn([
                'gift_id',
                'message_template_id',
                'created_by',
                'stage',
                'template_type',
                'custom_message',
                'custom_media_type',
                'custom_media_path',
                'client_filters',
                'target_client_count',
                'message_count',
                'platform',
                'whatsapp_token',
                'messaging_channel',
                'total_cost',
                'payment_status',
                'payment_id',
                'sent_count',
                'failed_count',
                'started_at',
                'completed_at',
                'requires_approval',
                'approved_by',
                'approved_at',
                'admin_notes',
            ]);
        });
    }
};
