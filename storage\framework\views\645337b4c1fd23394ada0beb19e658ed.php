<!DOCTYPE html>
<html lang="ar" dir="rtl" class="rtl">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>تسجيل الدخول - Gifts Saudi | هدايا السعودية</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom Auth CSS -->
    <link href="<?php echo e(asset('css/auth.css')); ?>" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>

<body class="rtl">
    <div class="login-container" dir="rtl">
        <div class="login-background">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
        </div>

        <!-- Language Switcher -->
        <div class="language-switcher">
            <a href="#" class="lang-btn active" onclick="switchLanguage('ar')">العربية</a>
            <a href="#" class="lang-btn" onclick="switchLanguage('en')">English</a>
        </div>

        <div class="login-card loaded">
            <div class="login-card-inner">
                <div class="login-card-front">
                    <!-- Logo -->
                    <div class="login-logo">
                        <span class="logo-text" data-ar="هدايا السعودية" data-en="Gifts Saudi">هدايا السعودية</span>
                    </div>

                    <!-- Title -->
                    <h1 class="login-title" data-ar="تسجيل الدخول" data-en="Login">تسجيل الدخول</h1>
                    <p class="login-subtitle" data-ar="مرحبًا بعودتك! يرجى تسجيل الدخول للوصول إلى حسابك" data-en="Welcome back! Please login to access your account">
                        مرحبًا بعودتك! يرجى تسجيل الدخول للوصول إلى حسابك
                    </p>

                    <!-- Session Status -->
                    <?php if(session('status')): ?>
                        <div class="success-message">
                            <?php echo e(session('status')); ?>

                        </div>
                    <?php endif; ?>

                    <!-- Validation Errors -->
                    <?php if($errors->any()): ?>
                        <div class="error-message">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo e($error); ?><br>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Login Form -->
                    <form method="POST" action="<?php echo e(route('login')); ?>" class="login-form">
                        <?php echo csrf_field(); ?>

                        <!-- Login Type Selection -->
                        <div class="form-group">
                            <label for="login_type" data-ar="نوع تسجيل الدخول" data-en="Login Type">نوع تسجيل الدخول</label>
                            <div class="input-with-icon">
                                <i class="fas fa-user-tag"></i>
                                <select 
                                    id="login_type" 
                                    name="login_type" 
                                    required
                                    class="role-select"
                                >
                                    <option value="" data-ar="اختر نوع الحساب" data-en="Select Account Type">اختر نوع الحساب</option>
                                    <option value="admin" data-ar="مدير النظام" data-en="Admin" <?php echo e(old('login_type') == 'admin' ? 'selected' : ''); ?>>مدير النظام</option>
                                    <option value="vendor" data-ar="متجر / تاجر" data-en="Vendor" <?php echo e(old('login_type') == 'vendor' ? 'selected' : ''); ?>>متجر / تاجر</option>
                                    <option value="staff" data-ar="موظف" data-en="Staff" <?php echo e(old('login_type') == 'staff' ? 'selected' : ''); ?>>موظف</option>
                                </select>
                            </div>
                        </div>

                        <!-- Email Address -->
                        <div class="form-group">
                            <label for="email" data-ar="البريد الإلكتروني" data-en="Email">البريد الإلكتروني</label>
                            <div class="input-with-icon">
                                <i class="fas fa-envelope"></i>
                                <input 
                                    id="email" 
                                    type="email" 
                                    name="email" 
                                    value="<?php echo e(old('email')); ?>" 
                                    required 
                                    autofocus 
                                    autocomplete="username"
                                    placeholder="أدخل بريدك الإلكتروني"
                                    data-placeholder-ar="أدخل بريدك الإلكتروني"
                                    data-placeholder-en="Enter your email"
                                />
                            </div>
                        </div>

                        <!-- Password -->
                        <div class="form-group">
                            <label for="password" data-ar="كلمة المرور" data-en="Password">كلمة المرور</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input 
                                    id="password" 
                                    type="password" 
                                    name="password" 
                                    required 
                                    autocomplete="current-password"
                                    placeholder="أدخل كلمة المرور"
                                    data-placeholder-ar="أدخل كلمة المرور"
                                    data-placeholder-en="Enter your password"
                                />
                            </div>
                        </div>

                        <!-- Form Options -->
                        <div class="form-options">
                            <div class="remember-me">
                                <input id="remember_me" type="checkbox" name="remember">
                                <label for="remember_me" data-ar="تذكرني" data-en="Remember me">تذكرني</label>
                            </div>

                            <?php if(Route::has('password.request')): ?>
                                <a class="forgot-password" href="<?php echo e(route('password.request')); ?>" data-ar="نسيت كلمة المرور؟" data-en="Forgot password?">
                                    نسيت كلمة المرور؟
                                </a>
                            <?php endif; ?>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="login-button" data-ar="تسجيل الدخول" data-en="Login">
                            تسجيل الدخول
                        </button>

                        <!-- Register Link -->
                        <div class="login-footer">
                            <span data-ar="ليس لديك حساب؟" data-en="Don't have an account?">ليس لديك حساب؟</span>
                            <?php if(Route::has('register')): ?>
                                <a href="<?php echo e(route('register')); ?>" class="register-link" data-ar="سجل الآن" data-en="Register now">
                                    سجل الآن
                                </a>
                            <?php endif; ?>
                        </div>

                        <!-- Back to Home -->
                        <div class="login-footer">
                            <a href="<?php echo e(url('/')); ?>" class="back-to-home" data-ar="العودة للصفحة الرئيسية" data-en="Back to Home">
                                <i class="fas fa-arrow-right rtl-flip"></i>
                                <span>العودة للصفحة الرئيسية</span>
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Language switching functionality
        function switchLanguage(lang) {
            const body = document.body;
            const html = document.documentElement;
            const elements = document.querySelectorAll('[data-ar][data-en]');
            const placeholderElements = document.querySelectorAll('[data-placeholder-ar][data-placeholder-en]');
            const langButtons = document.querySelectorAll('.lang-btn');

            // Update active language button
            langButtons.forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent.includes(lang === 'ar' ? 'العربية' : 'English')) {
                    btn.classList.add('active');
                }
            });

            // Update text direction and content
            if (lang === 'ar') {
                body.className = 'rtl';
                html.className = 'rtl';
                html.setAttribute('dir', 'rtl');
                html.setAttribute('lang', 'ar');
            } else {
                body.className = 'ltr';
                html.className = 'ltr';
                html.setAttribute('dir', 'ltr');
                html.setAttribute('lang', 'en');
            }

            // Update all translatable elements
            elements.forEach(element => {
                element.textContent = element.getAttribute('data-' + lang);
            });

            // Update placeholders
            placeholderElements.forEach(element => {
                element.placeholder = element.getAttribute('data-placeholder-' + lang);
            });

            // Update select options
            const selectOptions = document.querySelectorAll('option[data-ar][data-en]');
            selectOptions.forEach(option => {
                option.textContent = option.getAttribute('data-' + lang);
            });

            // Store language preference
            localStorage.setItem('preferred-language', lang);
        }

        // Load saved language preference
        document.addEventListener('DOMContentLoaded', function() {
            const savedLang = localStorage.getItem('preferred-language') || 'ar';
            switchLanguage(savedLang);
        });

        // Form submission handling
        document.querySelector('.login-form').addEventListener('submit', function(e) {
            const button = document.querySelector('.login-button');
            button.disabled = true;
            button.innerHTML = '<div class="spinner"></div>';
        });
    </script>
</body>
</html>
<?php /**PATH C:\hany\GiftsSaudi-Laravel (1)\GiftsSaudi-Laravel (1)\resources\views/auth/login.blade.php ENDPATH**/ ?>