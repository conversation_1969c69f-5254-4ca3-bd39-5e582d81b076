@extends('layouts.employee')

@section('page-title', 'البحث')

@section('content')
<!-- Search Section -->
<div class="search-section mb-4">
    <div class="search-card">
        <h5 class="search-title mb-3">
            <i class="fas fa-search me-2 text-success"></i>
            البحث برقم الجوال
        </h5>
        
        <form method="POST" action="{{ route('employee.search.submit') }}" id="searchForm">
            @csrf
            <div class="search-container">
                <input type="tel" 
                       class="search-input" 
                       id="phone" 
                       name="phone" 
                       value="{{ old('phone', $phone ?? '') }}"
                       placeholder="أدخل رقم الجوال..."
                       autocomplete="tel"
                       pattern="[0-9+\-\s]+"
                       required>
                <i class="search-icon fas fa-mobile-alt"></i>
            </div>
            
            <button type="submit" class="btn btn-success-mobile" id="searchBtn">
                <span class="btn-text">
                    <i class="fas fa-search me-2"></i>
                    بحث
                </span>
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    جاري البحث...
                </div>
            </button>
        </form>
    </div>
</div>

<!-- Search Tips -->
<div class="tips-section mb-4">
    <div class="tips-card">
        <h6 class="tips-title">
            <i class="fas fa-lightbulb me-2 text-warning"></i>
            نصائح للبحث
        </h6>
        <ul class="tips-list">
            <li>يمكنك البحث بأي جزء من رقم الجوال</li>
            <li>لا تحتاج لإدخال مفتاح الدولة (+966)</li>
            <li>تأكد من صحة الرقم قبل البحث</li>
            <li>يمكن البحث بالأرقام المحلية أو الدولية</li>
        </ul>
    </div>
</div>

<!-- Error Message -->
@if(isset($error))
<div class="error-section mb-4">
    <div class="alert-mobile alert-danger-mobile">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ $error }}
        
        @if(isset($client) && $client)
        <div class="mt-3">
            <h6 class="mb-2">معلومات العميل:</h6>
            <div class="client-info">
                <div class="info-row">
                    <strong>الاسم:</strong> {{ $client->name }}
                </div>
                <div class="info-row">
                    <strong>الجوال:</strong> {{ $client->phone_number }}
                </div>
                <div class="info-row">
                    <strong>المدينة:</strong> {{ $client->city ?? 'غير محدد' }}
                </div>
            </div>
        </div>
        @endif
        
        <div class="mt-3">
            <button class="btn btn-warning-mobile btn-sm" onclick="clearSearch()">
                <i class="fas fa-redo me-2"></i>
                بحث جديد
            </button>
        </div>
    </div>
</div>
@endif

<!-- Recent Searches -->
@if(!isset($phone) && !isset($error))
<div class="recent-section">
    <h6 class="recent-title mb-3">
        <i class="fas fa-history me-2 text-info"></i>
        عمليات بحث سريعة
    </h6>
    
    <div class="quick-search-grid">
        <button class="quick-search-btn" onclick="quickSearch('05')">
            <i class="fas fa-mobile-alt me-2"></i>
            05xxxxxxxx
        </button>
        <button class="quick-search-btn" onclick="quickSearch('054')">
            <i class="fas fa-mobile-alt me-2"></i>
            054xxxxxxx
        </button>
        <button class="quick-search-btn" onclick="quickSearch('055')">
            <i class="fas fa-mobile-alt me-2"></i>
            055xxxxxxx
        </button>
        <button class="quick-search-btn" onclick="quickSearch('056')">
            <i class="fas fa-mobile-alt me-2"></i>
            056xxxxxxx
        </button>
    </div>
    
    <div class="help-section mt-4">
        <div class="help-card">
            <div class="help-icon">
                <i class="fas fa-question-circle text-info"></i>
            </div>
            <div class="help-content">
                <h6 class="help-title">كيفية استخدام البحث؟</h6>
                <p class="help-text">
                    أدخل رقم جوال العميل في خانة البحث أعلاه. سيتم البحث في قاعدة البيانات 
                    وعرض الهدايا المتاحة للتسليم لهذا العميل.
                </p>
            </div>
        </div>
    </div>
</div>
@endif

<!-- Loading Spinner -->
<div class="loading-spinner text-center" style="display: none;">
    <div class="spinner-border text-success" role="status">
        <span class="visually-hidden">جاري البحث...</span>
    </div>
    <div class="mt-2">
        <small class="text-muted">جاري البحث في قاعدة البيانات...</small>
    </div>
</div>
@endsection

@push('styles')
<style>
.search-card, .tips-card, .help-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.search-title, .tips-title, .recent-title {
    color: #343a40;
    font-weight: 600;
    margin-bottom: 1rem;
}

.tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tips-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
    color: #6c757d;
    position: relative;
    padding-right: 1.5rem;
}

.tips-list li:before {
    content: "•";
    color: #28a745;
    font-weight: bold;
    position: absolute;
    right: 0;
}

.tips-list li:last-child {
    border-bottom: none;
}

.client-info .info-row {
    padding: 0.25rem 0;
    color: #6c757d;
}

.quick-search-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.quick-search-btn {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    border-radius: 10px;
    padding: 1rem;
    color: #28a745;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.quick-search-btn:hover {
    background: rgba(40, 167, 69, 0.2);
    transform: translateY(-2px);
}

.help-card {
    background: rgba(23, 162, 184, 0.05);
    border: 1px solid rgba(23, 162, 184, 0.2);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.help-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.help-title {
    color: #17a2b8;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.help-text {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

.loading-spinner {
    padding: 2rem;
}

/* Animation for search input */
.search-input:focus {
    animation: searchPulse 2s infinite;
}

@keyframes searchPulse {
    0% { box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25); }
    50% { box-shadow: 0 0 0 0.4rem rgba(40, 167, 69, 0.15); }
    100% { box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25); }
}

/* Mobile optimizations */
@media (max-width: 576px) {
    .search-card, .tips-card, .help-card {
        padding: 1rem;
    }
    
    .quick-search-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .quick-search-btn {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .help-card {
        flex-direction: column;
        text-align: center;
    }
}
</style>
@endpush

@push('scripts')
<script>
// Search form handling
document.getElementById('searchForm').addEventListener('submit', function(e) {
    const btn = document.getElementById('searchBtn');
    const btnText = btn.querySelector('.btn-text');
    const spinner = btn.querySelector('.loading-spinner');
    const loadingDiv = document.querySelector('.loading-spinner');
    
    btn.disabled = true;
    btnText.style.display = 'none';
    spinner.style.display = 'inline-block';
    loadingDiv.style.display = 'block';
});

// Quick search function
function quickSearch(prefix) {
    const phoneInput = document.getElementById('phone');
    phoneInput.value = prefix;
    phoneInput.focus();
    
    // Add some visual feedback
    phoneInput.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
    setTimeout(() => {
        phoneInput.style.backgroundColor = '';
    }, 500);
}

// Clear search function
function clearSearch() {
    const phoneInput = document.getElementById('phone');
    phoneInput.value = '';
    phoneInput.focus();
    
    // Hide error sections
    const errorSection = document.querySelector('.error-section');
    if (errorSection) {
        errorSection.style.display = 'none';
    }
}

// Phone input formatting
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/[^\d+]/g, '');
    
    // Basic formatting for Saudi numbers
    if (value.startsWith('966')) {
        value = '+' + value;
    } else if (value.startsWith('0') && value.length > 1) {
        // Keep as is for local format
    }
    
    e.target.value = value;
});

// Auto-focus on phone input when page loads
document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.getElementById('phone');
    if (phoneInput && !phoneInput.value) {
        setTimeout(() => {
            phoneInput.focus();
        }, 500);
    }
});
</script>
@endpush 