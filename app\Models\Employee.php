<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Employee extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'national_id',
        'gender',
        'birth_date',
        'vendor_id',
        'employee_id',
        'position',
        'employment_type',
        'hire_date',
        'salary',
        'address',
        'emergency_contact_name',
        'emergency_contact_phone',
        'status',
        'total_deliveries',
        'performance_rating',
        'notes',
        'password',
        'last_login_at',
        'profile_photo',
        'id_document',
        'contract_document'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'hire_date' => 'date',
        'last_login_at' => 'datetime',
        'salary' => 'decimal:2',
        'performance_rating' => 'decimal:2',
        'total_deliveries' => 'integer'
    ];

    // Constants
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_SUSPENDED = 'suspended';
    const STATUS_PENDING = 'pending';

    const EMPLOYMENT_FULL_TIME = 'full_time';
    const EMPLOYMENT_PART_TIME = 'part_time';
    const EMPLOYMENT_CONTRACT = 'contract';

    // Relationships
    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function deliveries()
    {
        return $this->hasMany(GiftDelivery::class);
    }

    public function successfulDeliveries()
    {
        return $this->hasMany(GiftDelivery::class)->where('status', 'delivered');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    public function scopeInactive($query)
    {
        return $query->where('status', self::STATUS_INACTIVE);
    }

    public function scopeSuspended($query)
    {
        return $query->where('status', self::STATUS_SUSPENDED);
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeFullTime($query)
    {
        return $query->where('employment_type', self::EMPLOYMENT_FULL_TIME);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    // Accessors
    public function getAgeAttribute()
    {
        return $this->birth_date ? $this->birth_date->age : null;
    }

    public function getYearsOfServiceAttribute()
    {
        return $this->hire_date ? $this->hire_date->diffInYears(now()) : 0;
    }

    public function getMonthsOfServiceAttribute()
    {
        return $this->hire_date ? $this->hire_date->diffInMonths(now()) : 0;
    }

    public function getStatusBadgeClassAttribute()
    {
        $classes = [
            self::STATUS_ACTIVE => 'badge-success',
            self::STATUS_INACTIVE => 'badge-secondary',
            self::STATUS_SUSPENDED => 'badge-danger',
            self::STATUS_PENDING => 'badge-warning'
        ];

        return $classes[$this->status] ?? 'badge-secondary';
    }

    public function getStatusDisplayNameAttribute()
    {
        $names = [
            self::STATUS_ACTIVE => 'نشط',
            self::STATUS_INACTIVE => 'غير نشط',
            self::STATUS_SUSPENDED => 'موقوف',
            self::STATUS_PENDING => 'في انتظار الموافقة'
        ];

        return $names[$this->status] ?? 'غير محدد';
    }

    public function getEmploymentTypeDisplayNameAttribute()
    {
        $names = [
            self::EMPLOYMENT_FULL_TIME => 'دوام كامل',
            self::EMPLOYMENT_PART_TIME => 'دوام جزئي',
            self::EMPLOYMENT_CONTRACT => 'عقد مؤقت'
        ];

        return $names[$this->employment_type] ?? 'غير محدد';
    }

    public function getGenderDisplayNameAttribute()
    {
        return $this->gender === 'male' ? 'ذكر' : 'أنثى';
    }

    public function getFormattedSalaryAttribute()
    {
        return $this->salary ? number_format($this->salary, 2) . ' ريال' : 'غير محدد';
    }

    public function getProfilePhotoUrlAttribute()
    {
        return $this->profile_photo 
            ? asset('storage/' . $this->profile_photo)
            : asset('images/default-avatar.png');
    }

    // Methods
    public function isActive()
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    public function isSuspended()
    {
        return $this->status === self::STATUS_SUSPENDED;
    }

    public function canLogin()
    {
        return $this->isActive() && !empty($this->password);
    }

    public function updatePerformanceRating()
    {
        $totalDeliveries = $this->deliveries()->count();
        $successfulDeliveries = $this->successfulDeliveries()->count();
        $avgRating = $this->deliveries()
            ->whereNotNull('client_rating')
            ->avg('client_rating');

        if ($totalDeliveries === 0) {
            $this->performance_rating = 0;
        } else {
            $successRate = ($successfulDeliveries / $totalDeliveries) * 100;
            $clientSatisfaction = $avgRating ?? 0;
            
            // Calculate performance: 70% success rate + 30% client satisfaction
            $performance = (($successRate / 100) * 0.7) + (($clientSatisfaction / 5) * 0.3);
            $this->performance_rating = round($performance * 5, 2); // Convert to 5-star scale
        }

        $this->total_deliveries = $totalDeliveries;
        $this->save();
    }

    public function getDeliveryStats($startDate = null, $endDate = null)
    {
        $query = $this->deliveries();
        
        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        $total = $query->count();
        $delivered = $query->where('status', 'delivered')->count();
        $pending = $query->where('status', 'pending')->count();
        $cancelled = $query->where('status', 'cancelled')->count();

        return [
            'total' => $total,
            'delivered' => $delivered,
            'pending' => $pending,
            'cancelled' => $cancelled,
            'success_rate' => $total > 0 ? round(($delivered / $total) * 100, 2) : 0
        ];
    }

    public function generateEmployeeId()
    {
        $vendorCode = strtoupper(substr($this->vendor->business_name ?? 'VEN', 0, 3));
        $year = date('Y');
        $lastEmployee = static::where('vendor_id', $this->vendor_id)
                             ->whereYear('created_at', $year)
                             ->count();
        
        $sequence = str_pad($lastEmployee + 1, 3, '0', STR_PAD_LEFT);
        
        return $vendorCode . $year . $sequence;
    }

    // Boot method to auto-generate employee ID
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($employee) {
            if (empty($employee->employee_id)) {
                $employee->employee_id = $employee->generateEmployeeId();
            }
        });
    }
}
