@extends('layouts.admin')

@section('title', 'تفاصيل التاجر')
@section('page-title', 'تفاصيل التاجر')

@push('styles')
<style>
    .detail-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        margin-bottom: 30px;
    }

    .vendor-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, #2C5530, #4A7C59);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        font-weight: bold;
        margin: 0 auto 20px;
    }

    .info-item {
        border-bottom: 1px solid #f8f9fa;
        padding: 15px 0;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .info-value {
        color: #6c757d;
        font-size: 1.1rem;
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .status-approved { background: #d4edda; color: #155724; }
    .status-pending { background: #fff3cd; color: #856404; }
    .status-rejected { background: #f8d7da; color: #721c24; }

    .social-link {
        display: inline-block;
        margin: 5px 10px 5px 0;
        padding: 8px 15px;
        background: #f8f9fa;
        border-radius: 20px;
        text-decoration: none;
        color: #495057;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .social-link:hover {
        background: #e9ecef;
        color: #495057;
        text-decoration: none;
    }

    .gifts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .gift-card {
        background: white;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .gift-card:hover {
        transform: translateY(-3px);
    }

    .gift-image {
        width: 100%;
        height: 150px;
        border-radius: 8px;
        object-fit: cover;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: #6c757d;
        margin-bottom: 10px;
    }
</style>
@endpush

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">معلومات التاجر</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center text-white mb-3" 
                         style="width: 100px; height: 100px; font-size: 2.5rem;">
                        {{ substr($vendor->company_name ?? $vendor->user->name, 0, 1) }}
                    </div>
                    <h3>{{ $vendor->company_name ?? $vendor->user->name }}</h3>
                    @if($vendor->is_approved)
                        <span class="badge bg-success">معتمد</span>
                    @else
                        <span class="badge bg-warning">في الانتظار</span>
                    @endif
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <strong>اسم التاجر:</strong> {{ $vendor->user->name }}<br>
                        <strong>البريد الإلكتروني:</strong> {{ $vendor->user->email }}<br>
                        @if($vendor->user->phone)
                            <strong>الهاتف:</strong> {{ $vendor->user->phone }}<br>
                        @endif
                        @if($vendor->city)
                            <strong>المدينة:</strong> {{ $vendor->city }}<br>
                        @endif
                    </div>
                    <div class="col-md-6">
                        @if($vendor->address)
                            <strong>العنوان:</strong> {{ $vendor->address }}<br>
                        @endif
                        <strong>تاريخ التسجيل:</strong> {{ $vendor->created_at->format('Y/m/d') }}<br>
                        <strong>آخر تحديث:</strong> {{ $vendor->updated_at->format('Y/m/d') }}<br>
                    </div>
                </div>

                @if($vendor->description)
                    <div class="mt-3">
                        <strong>وصف النشاط:</strong><br>
                        <p class="text-muted">{{ $vendor->description }}</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Social Media -->
        @if($vendor->website || $vendor->instagram || $vendor->twitter)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">روابط التواصل</h5>
                </div>
                <div class="card-body">
                    @if($vendor->website)
                        <a href="{{ $vendor->website }}" target="_blank" class="btn btn-outline-primary me-2 mb-2">
                            <i class="fas fa-globe"></i> الموقع الإلكتروني
                        </a>
                    @endif
                    @if($vendor->instagram)
                        <a href="{{ $vendor->instagram }}" target="_blank" class="btn btn-outline-primary me-2 mb-2">
                            <i class="fab fa-instagram"></i> إنستغرام
                        </a>
                    @endif
                    @if($vendor->twitter)
                        <a href="{{ $vendor->twitter }}" target="_blank" class="btn btn-outline-primary me-2 mb-2">
                            <i class="fab fa-twitter"></i> تويتر
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>

    <div class="col-lg-4">
        <!-- Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">الإجراءات</h5>
            </div>
            <div class="card-body d-grid gap-2">
                <a href="{{ route('admin.vendors.edit', $vendor) }}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                
                @if(!$vendor->is_approved)
                    <button type="button" class="btn btn-success" onclick="approveVendor({{ $vendor->id }})">
                        <i class="fas fa-check"></i> اعتماد
                    </button>
                @else
                    <button type="button" class="btn btn-warning" onclick="rejectVendor({{ $vendor->id }})">
                        <i class="fas fa-times"></i> رفض الاعتماد
                    </button>
                @endif
                
                <a href="{{ route('admin.vendors.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">الإحصائيات</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي الهدايا:</span>
                    <strong>{{ $vendor->gifts->count() }}</strong>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الهدايا المعتمدة:</span>
                    <strong>{{ $vendor->gifts->where('approved', true)->count() }}</strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>في الانتظار:</span>
                    <strong>{{ $vendor->gifts->where('approved', false)->count() }}</strong>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Gifts -->
@if($vendor->gifts->count() > 0)
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">هدايا التاجر</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الهدية</th>
                            <th>السعر</th>
                            <th>الفئة</th>
                            <th>الحالة</th>
                            <th>الاعتماد</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($vendor->gifts()->latest()->take(10)->get() as $gift)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center text-white" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-gift"></i>
                                        </div>
                                        <strong>{{ $gift->name }}</strong>
                                    </div>
                                </td>
                                <td>{{ number_format($gift->price, 2) }} ريال</td>
                                <td>{{ $gift->category }}</td>
                                <td>
                                    @switch($gift->status)
                                        @case('available')
                                            <span class="badge bg-success">متاح</span>
                                            @break
                                        @case('out_of_stock')
                                            <span class="badge bg-warning">نفد المخزون</span>
                                            @break
                                        @default
                                            <span class="badge bg-secondary">{{ $gift->status }}</span>
                                    @endswitch
                                </td>
                                <td>
                                    @if($gift->approved)
                                        <span class="badge bg-success">معتمد</span>
                                    @else
                                        <span class="badge bg-warning">في الانتظار</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('admin.gifts.show', $gift) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            @if($vendor->gifts->count() > 10)
                <div class="text-center mt-3">
                    <a href="{{ route('admin.gifts.index', ['vendor_id' => $vendor->id]) }}" class="btn btn-outline-primary">
                        عرض جميع الهدايا ({{ $vendor->gifts->count() }})
                    </a>
                </div>
            @endif
        </div>
    </div>
@endif
@endsection

@push('scripts')
<script>
function approveVendor(vendorId) {
    if (confirm('هل أنت متأكد من اعتماد هذا التاجر؟')) {
        fetch(`/admin/vendors/${vendorId}/approve`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في العملية');
            }
        });
    }
}

function rejectVendor(vendorId) {
    if (confirm('هل أنت متأكد من رفض اعتماد هذا التاجر؟')) {
        fetch(`/admin/vendors/${vendorId}/reject`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في العملية');
            }
        });
    }
}
</script>
@endpush 