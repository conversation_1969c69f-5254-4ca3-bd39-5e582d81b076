<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class GiftCampaign extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'gift_id',
        'vendor_id',
        'message_template_id',
        'created_by',
        'campaign_name',
        'campaign_type',
        'campaign_description',
        'stage',
        'template_type',
        'custom_message',
        'custom_media_type',
        'custom_media_path',
        'client_filters',
        'target_client_count',
        'message_count',
        'platform',
        'whatsapp_token',
        'messaging_channel',
        'total_cost',
        'payment_status',
        'payment_id',
        'sent_count',
        'failed_count',
        'started_at',
        'completed_at',
        'requires_approval',
        'approved_by',
        'approved_at',
        'admin_notes',
    ];

    protected $casts = [
        'client_filters' => 'array',
        'target_client_count' => 'integer',
        'message_count' => 'integer',
        'total_cost' => 'decimal:2',
        'sent_count' => 'integer',
        'failed_count' => 'integer',
        'requires_approval' => 'boolean',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function gift(): BelongsTo
    {
        return $this->belongsTo(Gift::class);
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    public function messageTemplate(): BelongsTo
    {
        return $this->belongsTo(MessageTemplate::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopePendingApproval($query)
    {
        return $query->where('stage', 'pending_approval');
    }

    public function scopeRequiresApproval($query)
    {
        return $query->where('requires_approval', true)
                    ->whereNull('approved_at');
    }

    public function scopeCompleted($query)
    {
        return $query->where('stage', 'completed');
    }

    public function scopeInProgress($query)
    {
        return $query->where('stage', 'processing');
    }

    public function scopeActive($query)
    {
        return $query->whereNotIn('stage', ['completed', 'failed']);
    }

    // Helper methods
    public function canProceedToNextStage(): bool
    {
        return match ($this->stage) {
            'gift_info' => $this->gift_id !== null,
            'template_selection' => $this->template_type !== null && 
                                   ($this->template_type === 'prebuilt' ? $this->message_template_id !== null : $this->custom_message !== null),
            'client_filtering' => $this->client_filters !== null && $this->target_client_count > 0,
            'message_count' => $this->message_count > 0 && $this->message_count <= $this->target_client_count,
            'platform_selection' => $this->platform !== null && 
                                   ($this->platform === 'own' || ($this->platform === 'external' && $this->whatsapp_token !== null)),
            'payment' => $this->platform === 'external' || $this->payment_status === 'paid',
            'pending_approval' => $this->approved_at !== null,
            default => false,
        };
    }

    public function getNextStage(): ?string
    {
        return match ($this->stage) {
            'gift_info' => 'template_selection',
            'template_selection' => 'client_filtering',
            'client_filtering' => 'message_count',
            'message_count' => 'platform_selection',
            'platform_selection' => $this->platform === 'external' ? 'pending_approval' : 'payment',
            'payment' => 'approved',
            'pending_approval' => 'approved',
            'approved' => 'processing',
            'processing' => 'completed',
            default => null,
        };
    }

    public function calculateCost(): float
    {
        // Cost calculation based on platform and message count
        if ($this->platform === 'external') {
            return 0; // No cost for external platform
        }
        
        // Our platform pricing (example: 0.5 SAR per message)
        $costPerMessage = 0.5;
        return $this->message_count * $costPerMessage;
    }

    public function isApprovalRequired(): bool
    {
        return $this->platform === 'external';
    }

    public function getProgressPercentage(): int
    {
        $stages = [
            'gift_info' => 10,
            'template_selection' => 25,
            'client_filtering' => 40,
            'message_count' => 55,
            'platform_selection' => 70,
            'payment' => 85,
            'pending_approval' => 90,
            'approved' => 95,
            'processing' => 97,
            'completed' => 100,
            'failed' => 0,
        ];

        return $stages[$this->stage] ?? 0;
    }

    public function getStageDisplayName(): string
    {
        $stages = [
            'gift_info' => 'معلومات الهدية',
            'template_selection' => 'اختيار القالب',
            'client_filtering' => 'تصفية العملاء',
            'message_count' => 'عدد الرسائل',
            'platform_selection' => 'اختيار المنصة',
            'payment' => 'الدفع',
            'pending_approval' => 'في انتظار الموافقة',
            'approved' => 'معتمد',
            'processing' => 'قيد الإرسال',
            'completed' => 'مكتمل',
            'failed' => 'فشل',
        ];

        return $stages[$this->stage] ?? $this->stage;
    }
}
