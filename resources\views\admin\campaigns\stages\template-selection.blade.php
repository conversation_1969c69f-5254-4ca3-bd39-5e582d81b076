<input type="hidden" name="template_type" id="template_type" value="{{ $campaign->template_type ?? 'prebuilt' }}">
<input type="hidden" name="message_template_id" id="message_template_id" value="{{ $campaign->message_template_id ?? '' }}">

<div class="form-section">
    <h3 class="section-title">
        <i class="fas fa-envelope stage-icon"></i>
        اختيار قالب الرسالة
    </h3>

    <div class="template-grid">
        <!-- Prebuilt Templates -->
        @foreach($messageTemplates as $template)
            <div class="template-option {{ $campaign->message_template_id == $template->id ? 'selected' : '' }}" 
                 data-template-id="{{ $template->id }}" 
                 data-template-type="prebuilt">
                <div class="d-flex align-items-start">
                    <div class="me-3">
                        <i class="fas fa-{{ $template->type == 'welcome' ? 'hand-sparkles' : ($template->type == 'birthday' ? 'birthday-cake' : ($template->type == 'promotional' ? 'percentage' : ($template->type == 'loyalty' ? 'star' : ($template->type == 'seasonal' ? 'snowflake' : 'gift')))) }} fa-2x text-orange"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="text-white mb-2">{{ $template->name }}</h5>
                        <p class="text-muted mb-3">{{ Str::limit($template->content, 100) }}</p>
                        <div class="template-meta">
                            <small class="text-muted">
                                <i class="fas fa-tag me-1"></i>
                                {{ $template->getTypeDisplayName() }}
                            </small>
                            @if($template->media_type)
                                <small class="text-muted ms-3">
                                    <i class="fas fa-{{ $template->media_type == 'image' ? 'image' : ($template->media_type == 'video' ? 'video' : 'music') }} me-1"></i>
                                    {{ $template->getMediaTypeDisplayName() }}
                                </small>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @endforeach

        <!-- Custom Template Option -->
        <div class="template-option {{ $campaign->template_type == 'custom' ? 'selected' : '' }}" 
             data-template-type="custom" 
             data-template-id="">
            <div class="text-center">
                <i class="fas fa-plus-circle fa-3x text-orange mb-3"></i>
                <h5 class="text-white mb-2">إنشاء قالب مخصص</h5>
                <p class="text-muted">قم بإنشاء رسالة مخصصة مع إمكانية إضافة وسائط</p>
            </div>
        </div>
    </div>
</div>

<!-- Custom Message Section -->
<div class="form-section" id="customMessageSection" style="display: {{ $campaign->template_type == 'custom' ? 'block' : 'none' }};">
    <h3 class="section-title">
        <i class="fas fa-edit stage-icon"></i>
        إنشاء رسالة مخصصة
    </h3>

    <div class="row">
        <div class="col-md-12">
            <label for="custom_message" class="form-label">نص الرسالة</label>
            <textarea name="custom_message" id="custom_message" 
                      class="form-control" 
                      rows="6" 
                      placeholder="اكتب رسالتك هنا... يمكنك استخدام المتغيرات مثل {name} و {gift_name} و {price}">{{ $campaign->custom_message }}</textarea>
            <div class="text-muted mt-2">
                <small>
                    <strong>المتغيرات المتاحة:</strong>
                    {name} - اسم العميل،
                    {gift_name} - اسم الهدية،
                    {price} - سعر الهدية،
                    {vendor_name} - اسم المورد
                </small>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-6">
            <label for="custom_media_type" class="form-label">نوع الوسائط (اختياري)</label>
            <select name="custom_media_type" id="custom_media_type" class="form-select">
                <option value="">بدون وسائط</option>
                <option value="image" {{ $campaign->custom_media_type == 'image' ? 'selected' : '' }}>صورة</option>
                <option value="video" {{ $campaign->custom_media_type == 'video' ? 'selected' : '' }}>فيديو</option>
                <option value="audio" {{ $campaign->custom_media_type == 'audio' ? 'selected' : '' }}>صوت</option>
            </select>
        </div>
        <div class="col-md-6">
            <label class="form-label">رفع الوسائط</label>
            <div class="custom-media-upload" id="customMediaUpload">
                @if($campaign->custom_media_path)
                    <i class="fas fa-file-upload fa-2x text-success mb-2"></i>
                    <p class="text-success mb-0">تم رفع الملف مسبقاً</p>
                @else
                    <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">انقر لرفع الملف</p>
                @endif
            </div>
            <input type="file" id="customMediaFile" name="custom_media_file" 
                   accept="image/*,video/*,audio/*" style="display: none;">
        </div>
    </div>
</div>

<!-- Message Preview -->
@if($campaign->gift)
    <div class="form-section">
        <h3 class="section-title">
            <i class="fas fa-eye stage-icon"></i>
            معاينة الرسالة
        </h3>
        
        <div class="message-preview p-4 rounded" style="background: rgba(42, 42, 42, 0.6); border: 1px solid #555;">
            <div class="d-flex align-items-start">
                <div class="me-3">
                    <div class="rounded-circle bg-success d-flex align-items-center justify-content-center" 
                         style="width: 40px; height: 40px;">
                        <i class="fab fa-whatsapp text-white"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="message-bubble p-3 rounded" 
                         style="background: #dcf8c6; color: #000; max-width: 80%;">
                        <div id="messagePreview">
                            @if($campaign->template_type == 'custom' && $campaign->custom_message)
                                {!! nl2br(e(str_replace(['{name}', '{gift_name}', '{price}', '{vendor_name}'], 
                                    ['أحمد السعودي', $campaign->gift->name, number_format($campaign->gift->price), $campaign->gift->vendor->business_name ?? 'المورد'], 
                                    $campaign->custom_message))) !!}
                            @elseif($campaign->messageTemplate)
                                {!! nl2br(e(str_replace(['{name}', '{gift_name}', '{price}', '{vendor_name}'], 
                                    ['أحمد السعودي', $campaign->gift->name, number_format($campaign->gift->price), $campaign->gift->vendor->business_name ?? 'المورد'], 
                                    $campaign->messageTemplate->content))) !!}
                            @else
                                مرحباً {name}! لديك هدية رائعة بانتظارك: {gift_name} بسعر {price} ريال من {vendor_name}
                            @endif
                        </div>
                        @if(($campaign->template_type == 'custom' && $campaign->custom_media_path) || ($campaign->messageTemplate && $campaign->messageTemplate->media_path))
                            <div class="mt-2">
                                <i class="fas fa-paperclip me-1"></i>
                                <small class="text-muted">مرفق وسائط</small>
                            </div>
                        @endif
                    </div>
                    <small class="text-muted">الآن</small>
                </div>
            </div>
        </div>
    </div>
@endif

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Template selection handlers
    document.querySelectorAll('.template-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.template-option').forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            
            const templateType = this.getAttribute('data-template-type');
            const templateId = this.getAttribute('data-template-id');
            
            document.querySelector('[name="template_type"]').value = templateType;
            document.querySelector('[name="message_template_id"]').value = templateId || '';
            
            // Show/hide custom message section
            const customSection = document.getElementById('customMessageSection');
            if (templateType === 'custom') {
                customSection.style.display = 'block';
            } else {
                customSection.style.display = 'none';
            }
            
            updateMessagePreview();
        });
    });
    
    // Custom message preview update
    document.getElementById('custom_message')?.addEventListener('input', updateMessagePreview);
    
    function updateMessagePreview() {
        const preview = document.getElementById('messagePreview');
        const templateType = document.querySelector('[name="template_type"]').value;
        
        if (templateType === 'custom') {
            const customMessage = document.getElementById('custom_message')?.value || '';
            const giftName = @json($campaign->gift->name ?? '');
            const giftPrice = @json(number_format($campaign->gift->price ?? 0));
            const vendorName = @json($campaign->gift->vendor->business_name ?? 'المورد');
            
            let previewText = customMessage.replace(/\{name\}/g, 'أحمد السعودي')
                                          .replace(/\{gift_name\}/g, giftName)
                                          .replace(/\{price\}/g, giftPrice)
                                          .replace(/\{vendor_name\}/g, vendorName);
            
            preview.innerHTML = previewText.replace(/\n/g, '<br>');
        } else {
            // Handle prebuilt template preview
            const selectedTemplate = document.querySelector('.template-option.selected[data-template-type="prebuilt"]');
            if (selectedTemplate) {
                const templateId = selectedTemplate.getAttribute('data-template-id');
                
                // Get template content from the templates array
                const templates = @json($messageTemplates->keyBy('id'));
                if (templates[templateId]) {
                    const template = templates[templateId];
                    const giftName = @json($campaign->gift->name ?? '');
                    const giftPrice = @json(number_format($campaign->gift->price ?? 0));
                    const vendorName = @json($campaign->gift->vendor->business_name ?? 'المورد');
                    
                    let previewText = template.content.replace(/\{name\}/g, 'أحمد السعودي')
                                                     .replace(/\{gift_name\}/g, giftName)
                                                     .replace(/\{price\}/g, giftPrice)
                                                     .replace(/\{vendor_name\}/g, vendorName);
                    
                    preview.innerHTML = previewText.replace(/\n/g, '<br>');
                }
            } else {
                // Default preview message
                const giftName = @json($campaign->gift->name ?? '');
                const giftPrice = @json(number_format($campaign->gift->price ?? 0));
                const vendorName = @json($campaign->gift->vendor->business_name ?? 'المورد');
                
                let defaultMessage = 'مرحباً أحمد السعودي! لديك هدية رائعة بانتظارك: ' + giftName + ' بسعر ' + giftPrice + ' ريال من ' + vendorName;
                preview.innerHTML = defaultMessage;
            }
        }
    }
    
    // Media upload functionality
    document.getElementById('customMediaUpload')?.addEventListener('click', function() {
        document.getElementById('customMediaFile').click();
    });
    
    document.getElementById('customMediaFile')?.addEventListener('change', function() {
        const file = this.files[0];
        const uploadArea = document.getElementById('customMediaUpload');
        
        if (file) {
            uploadArea.innerHTML = `
                <i class="fas fa-file-upload fa-2x text-success mb-2"></i>
                <p class="text-success mb-0">تم اختيار: ${file.name}</p>
            `;
        }
    });
    
    // Initialize preview on load
    updateMessagePreview();
    
    // Auto-select first template if none is selected
    const selectedTemplate = document.querySelector('.template-option.selected');
    if (!selectedTemplate) {
        const firstTemplate = document.querySelector('.template-option[data-template-type="prebuilt"]');
        if (firstTemplate) {
            firstTemplate.click();
        }
    }
    
    // Form validation before submission
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const templateType = document.querySelector('[name="template_type"]').value;
            const messageTemplateId = document.querySelector('[name="message_template_id"]').value;
            const customMessage = document.querySelector('[name="custom_message"]')?.value;
            
            console.log('Current form state:', {
                template_type: templateType,
                message_template_id: messageTemplateId,
                custom_message: customMessage,
                selected_template: document.querySelector('.template-option.selected')?.getAttribute('data-template-id')
            });
            
            if (!templateType) {
                e.preventDefault();
                alert('يرجى اختيار نوع القالب');
                return false;
            }
            
            if (templateType === 'prebuilt') {
                if (!messageTemplateId) {
                    e.preventDefault();
                    alert('يرجى اختيار قالب رسالة');
                    return false;
                }
                // Clear custom message field for prebuilt templates
                const customMessageField = document.querySelector('[name="custom_message"]');
                if (customMessageField) {
                    customMessageField.value = '';
                }
            } else if (templateType === 'custom') {
                if (!customMessage || customMessage.trim() === '') {
                    e.preventDefault();
                    alert('يرجى كتابة نص الرسالة المخصصة');
                    return false;
                }
                if (customMessage.trim().length < 10) {
                    e.preventDefault();
                    alert('يجب أن يكون نص الرسالة المخصصة 10 أحرف على الأقل');
                    return false;
                }
                // Clear template ID for custom messages
                document.querySelector('[name="message_template_id"]').value = '';
            }
            
            console.log('Final form data being submitted:', {
                template_type: templateType,
                message_template_id: document.querySelector('[name="message_template_id"]').value,
                custom_message: document.querySelector('[name="custom_message"]')?.value
            });
        });
    }
});
</script> 