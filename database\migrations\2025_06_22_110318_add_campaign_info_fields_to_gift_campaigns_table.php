<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gift_campaigns', function (Blueprint $table) {
            $table->string('campaign_name')->nullable()->after('created_by');
            $table->enum('campaign_type', ['promotional', 'seasonal', 'loyalty', 'birthday', 'appreciation'])->nullable()->after('campaign_name');
            $table->text('campaign_description')->nullable()->after('campaign_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gift_campaigns', function (Blueprint $table) {
            $table->dropColumn(['campaign_name', 'campaign_type', 'campaign_description']);
        });
    }
};
