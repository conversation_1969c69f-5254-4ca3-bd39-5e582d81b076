<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="utf-8">
    <title>Gifts Saudi | هدايا السعودية - منصة إدارة الهدايا الذكية</title>

    <!-- Mobile Responsive Meta -->
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

    <!-- Theme Meta -->
    <meta name="theme-name" content="Gifts Saudi" />
    <meta name="description" content="منصة ذكية لإدارة وتوصيل الهدايا في المملكة العربية السعودية مع تكامل واتساب">

    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic & English -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #ff1493;
            --secondary-color: #6a1b9a;
            --dark-color: #1a0033;
            --light-color: #ffffff;
            --accent-color: #ff9800;
            --text-color: #333;
            --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
            --font-heading: 'Poppins', 'Cairo', sans-serif;
            --font-body: 'Cairo', 'Poppins', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-body);
            line-height: 1.6;
            color: var(--text-color);
            overflow-x: hidden;
        }

        .rtl {
            direction: rtl;
            text-align: right;
        }

        .ltr {
            direction: ltr;
            text-align: left;
        }

        /* Language Switcher */
        .language-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }

        .lang-btn {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: var(--light-color);
            padding: 8px 16px;
            border-radius: 25px;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
            font-size: 0.9rem;
            text-decoration: none;
        }

        .lang-btn.active {
            background: var(--light-color);
            color: var(--primary-color);
            font-weight: 600;
        }

        .lang-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow);
            color: var(--light-color);
        }

        .lang-btn.active:hover {
            color: var(--primary-color);
        }

        /* Navigation */
        .navbar-custom {
            background: linear-gradient(135deg, var(--dark-color) 0%, var(--secondary-color) 100%);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            padding: 15px 0;
        }

        .navbar-brand img {
            height: 50px;
            filter: brightness(0) invert(1);
        }

        .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            margin: 0 0.25rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--dark-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            filter: blur(60px);
            opacity: 0.4;
            animation: float 15s infinite ease-in-out;
        }

        .shape-1 {
            width: 300px;
            height: 300px;
            background: var(--primary-color);
            top: -100px;
            left: -100px;
            animation-delay: 0s;
        }

        .shape-2 {
            width: 400px;
            height: 400px;
            background: var(--secondary-color);
            bottom: -150px;
            right: -150px;
            animation-delay: 5s;
        }

        .shape-3 {
            width: 200px;
            height: 200px;
            background: var(--accent-color);
            top: 50%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape-4 {
            width: 250px;
            height: 250px;
            background: var(--primary-color);
            bottom: 10%;
            left: 10%;
            animation-delay: 7s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0) translateX(0);
            }
            25% {
                transform: translateY(-20px) translateX(10px);
            }
            50% {
                transform: translateY(10px) translateX(-15px);
            }
            75% {
                transform: translateY(15px) translateX(5px);
            }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 900;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 1.5rem;
            font-family: var(--font-heading);
        }

        .hero-subtitle {
            font-size: 1.4rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 2rem;
            line-height: 1.8;
        }

        .btn-hero {
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            border: none;
            padding: 15px 35px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            color: white;
            box-shadow: 0 8px 25px rgba(255, 20, 147, 0.4);
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .btn-hero:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 20, 147, 0.6);
            color: white;
        }

        /* Services Section */
        .services-section {
            padding: 100px 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .service-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: var(--box-shadow);
            transition: all 0.4s ease;
            transform-style: preserve-3d;
            position: relative;
            overflow: hidden;
            margin-bottom: 2rem;
            height: 100%;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            border-radius: 20px 20px 0 0;
        }

        .service-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
            box-shadow: 0 8px 25px rgba(255, 20, 147, 0.3);
            transition: all 0.3s ease;
        }

        .service-card:hover .service-icon {
            transform: scale(1.1);
            background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        }

        .service-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--dark-color);
        }

        .service-description {
            color: #666;
            line-height: 1.8;
        }

        /* Statistics Section */
        .stats-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: 80px 0;
            position: relative;
        }

        .stat-item {
            text-align: center;
            color: white;
            padding: 2rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Features Section */
        .features-section {
            padding: 100px 0;
            background: white;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 15px;
            transition: var(--transition);
        }

        .feature-item:hover {
            background: #f8f9fa;
            transform: translateX(10px);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .rtl .feature-icon {
            margin-left: 0;
            margin-right: 1.5rem;
        }

        .feature-content h4 {
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .feature-content p {
            color: #666;
            margin: 0;
        }

        /* Footer */
        .footer {
            background: var(--dark-color);
            color: white;
            padding: 50px 0 20px;
        }

        .footer h5 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .footer a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: var(--transition);
        }

        .footer a:hover {
            color: var(--primary-color);
        }

        /* Section Title */
        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1rem;
            color: var(--dark-color);
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Login Button */
        .login-btn {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            text-decoration: none;
            transition: var(--transition);
            font-weight: 500;
        }

        .login-btn:hover {
            background: white;
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .language-switcher {
                top: 10px;
                right: 10px;
            }
            
            .service-card {
                margin-bottom: 2rem;
            }
            
            .btn-hero {
                padding: 12px 25px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .lang-btn {
                padding: 6px 12px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>

<body class="rtl">
    <!-- Language Switcher -->
    <div class="language-switcher">
        <a href="#" class="lang-btn active" onclick="switchLanguage('ar')">العربية</a>
        <a href="#" class="lang-btn" onclick="switchLanguage('en')">English</a>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <span style="font-size: 1.5rem; font-weight: bold; color: white;">Gifts Saudi</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <i class="fas fa-bars text-white"></i>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home" data-ar="الرئيسية" data-en="Home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services" data-ar="الخدمات" data-en="Services">الخدمات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features" data-ar="المميزات" data-en="Features">المميزات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact" data-ar="اتصل بنا" data-en="Contact">اتصل بنا</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="<?php echo e(route('login')); ?>" class="login-btn" data-ar="تسجيل الدخول" data-en="Login">تسجيل الدخول</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="hero-background">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
        </div>
        
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content" data-aos="fade-right">
                        <h1 class="hero-title" data-ar="هدايا السعودية" data-en="Gifts Saudi">هدايا السعودية</h1>
                        <p class="hero-subtitle" data-ar="منصة ذكية لإدارة وتوصيل الهدايا في المملكة العربية السعودية مع تكامل واتساب للتواصل المباشر مع العملاء" data-en="Smart platform for managing and delivering gifts in Saudi Arabia with WhatsApp integration for direct customer communication">
                            منصة ذكية لإدارة وتوصيل الهدايا في المملكة العربية السعودية مع تكامل واتساب للتواصل المباشر مع العملاء
                        </p>
                        <div class="hero-buttons">
                            <a href="<?php echo e(route('register')); ?>" class="btn-hero" data-ar="ابدأ الآن" data-en="Get Started">ابدأ الآن</a>
                            <a href="<?php echo e(route('login')); ?>" class="btn-hero" data-ar="تسجيل الدخول" data-en="Login">تسجيل الدخول</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image" data-aos="fade-left">
                        <img src="https://via.placeholder.com/500x400/ff1493/ffffff?text=🎁" alt="Gifts" class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services-section">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h2 class="section-title" data-ar="خدماتنا المميزة" data-en="Our Premium Services">خدماتنا المميزة</h2>
                    <p class="section-subtitle" data-ar="منصة هدايا السعودية توفر مجموعة متكاملة من الخدمات لمساعدة بائعي الهدايا على إدارة أعمالهم بكفاءة" data-en="Gifts Saudi platform provides a comprehensive set of services to help gift sellers manage their business efficiently">
                        منصة هدايا السعودية توفر مجموعة متكاملة من الخدمات لمساعدة بائعي الهدايا على إدارة أعمالهم بكفاءة
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <h4 class="service-title" data-ar="إدارة كتالوج الهدايا" data-en="Gift Catalog Management">إدارة كتالوج الهدايا</h4>
                        <p class="service-description" data-ar="أنشئ كتالوجًا احترافيًا لمنتجاتك مع خيارات متعددة للتصنيف والتصفية، مما يسهل على العملاء اختيار الهدايا المناسبة" data-en="Create a professional catalog for your products with multiple classification and filtering options, making it easier for customers to choose suitable gifts">
                            أنشئ كتالوجًا احترافيًا لمنتجاتك مع خيارات متعددة للتصنيف والتصفية، مما يسهل على العملاء اختيار الهدايا المناسبة
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fab fa-whatsapp"></i>
                        </div>
                        <h4 class="service-title" data-ar="تواصل عبر واتساب" data-en="WhatsApp Communication">تواصل عبر واتساب</h4>
                        <p class="service-description" data-ar="تكامل سلس مع تطبيق واتساب يتيح لك التواصل مباشرة مع العملاء، وإرسال عروض الهدايا، ومتابعة طلباتهم بكل سهولة" data-en="Seamless integration with WhatsApp allows you to communicate directly with customers, send gift offers, and track their orders easily">
                            تكامل سلس مع تطبيق واتساب يتيح لك التواصل مباشرة مع العملاء، وإرسال عروض الهدايا، ومتابعة طلباتهم بكل سهولة
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <h4 class="service-title" data-ar="إدارة الطلبات والمخزون" data-en="Order & Inventory Management">إدارة الطلبات والمخزون</h4>
                        <p class="service-description" data-ar="تتبع المخزون والطلبات في الوقت الفعلي، مع إشعارات آلية عند انخفاض المخزون وتقارير تفصيلية عن المبيعات" data-en="Track inventory and orders in real-time, with automatic notifications when inventory is low and detailed sales reports">
                            تتبع المخزون والطلبات في الوقت الفعلي، مع إشعارات آلية عند انخفاض المخزون وتقارير تفصيلية عن المبيعات
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="400">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <h4 class="service-title" data-ar="خيارات دفع متعددة" data-en="Multiple Payment Options">خيارات دفع متعددة</h4>
                        <p class="service-description" data-ar="دعم لمختلف وسائل الدفع الإلكترونية مع إمكانية توليد روابط دفع مباشرة عبر واتساب" data-en="Support for various electronic payment methods with the ability to generate direct payment links via WhatsApp">
                            دعم لمختلف وسائل الدفع الإلكترونية مع إمكانية توليد روابط دفع مباشرة عبر واتساب
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="500">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4 class="service-title" data-ar="تحليلات متقدمة" data-en="Advanced Analytics">تحليلات متقدمة</h4>
                        <p class="service-description" data-ar="احصل على رؤى قيمة حول سلوك العملاء وتفضيلات الهدايا الأكثر مبيعًا لاتخاذ قرارات أفضل" data-en="Get valuable insights into customer behavior and best-selling gift preferences to make better decisions">
                            احصل على رؤى قيمة حول سلوك العملاء وتفضيلات الهدايا الأكثر مبيعًا لاتخاذ قرارات أفضل
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="600">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h4 class="service-title" data-ar="إدارة المناسبات" data-en="Event Management">إدارة المناسبات</h4>
                        <p class="service-description" data-ar="نظام تذكير بالمناسبات يساعدك على التواصل مع العملاء في الأوقات المناسبة لتقديم هداياهم" data-en="Event reminder system helps you communicate with customers at the right times to present their gifts">
                            نظام تذكير بالمناسبات يساعدك على التواصل مع العملاء في الأوقات المناسبة لتقديم هداياهم
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section" data-aos="fade-up">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">500+</span>
                        <span class="stat-label" data-ar="متجر مسجل" data-en="Registered Stores">متجر مسجل</span>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">10K+</span>
                        <span class="stat-label" data-ar="هدية تم توصيلها" data-en="Gifts Delivered">هدية تم توصيلها</span>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">50+</span>
                        <span class="stat-label" data-ar="مدينة مغطاة" data-en="Cities Covered">مدينة مغطاة</span>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">98%</span>
                        <span class="stat-label" data-ar="رضا العملاء" data-en="Customer Satisfaction">رضا العملاء</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h2 class="section-title" data-ar="لماذا تختار هدايا السعودية؟" data-en="Why Choose Gifts Saudi?">لماذا تختار هدايا السعودية؟</h2>
                    <p class="section-subtitle" data-ar="نحن نقدم الحلول الأكثر تطورًا وسهولة في الاستخدام لإدارة أعمال الهدايا" data-en="We provide the most advanced and user-friendly solutions for managing gift businesses">
                        نحن نقدم الحلول الأكثر تطورًا وسهولة في الاستخدام لإدارة أعمال الهدايا
                    </p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-6">
                    <div class="feature-item" data-aos="fade-right" data-aos-delay="100">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="feature-content">
                            <h4 data-ar="تطبيق واتساب المألوف" data-en="Familiar WhatsApp Interface">تطبيق واتساب المألوف</h4>
                            <p data-ar="استفد من تطبيق واتساب الذي يستخدمه الجميع للتواصل مع عملائك دون الحاجة إلى تطبيقات إضافية" data-en="Leverage WhatsApp that everyone uses to communicate with your customers without needing additional apps">
                                استفد من تطبيق واتساب الذي يستخدمه الجميع للتواصل مع عملائك دون الحاجة إلى تطبيقات إضافية
                            </p>
                        </div>
                    </div>
                    
                    <div class="feature-item" data-aos="fade-right" data-aos-delay="200">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="feature-content">
                            <h4 data-ar="أمان وموثوقية" data-en="Security & Reliability">أمان وموثوقية</h4>
                            <p data-ar="نظام آمن ومشفر لحماية بيانات عملائك ومعاملاتهم المالية مع ضمان الخصوصية التامة" data-en="Secure and encrypted system to protect your customers' data and financial transactions with complete privacy guarantee">
                                نظام آمن ومشفر لحماية بيانات عملائك ومعاملاتهم المالية مع ضمان الخصوصية التامة
                            </p>
                        </div>
                    </div>
                    
                    <div class="feature-item" data-aos="fade-right" data-aos-delay="300">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <div class="feature-content">
                            <h4 data-ar="دعم فني متميز" data-en="Excellent Technical Support">دعم فني متميز</h4>
                            <p data-ar="فريق دعم فني متاح على مدار الساعة لمساعدتك في أي استفسار أو مشكلة تقنية" data-en="24/7 technical support team available to help you with any questions or technical issues">
                                فريق دعم فني متاح على مدار الساعة لمساعدتك في أي استفسار أو مشكلة تقنية
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="feature-item" data-aos="fade-left" data-aos-delay="100">
                        <div class="feature-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="feature-content">
                            <h4 data-ar="سهولة الاستخدام" data-en="Easy to Use">سهولة الاستخدام</h4>
                            <p data-ar="واجهة بسيطة وسهلة الاستخدام تمكنك من إدارة متجرك دون الحاجة لخبرة تقنية معقدة" data-en="Simple and easy-to-use interface allows you to manage your store without needing complex technical expertise">
                                واجهة بسيطة وسهلة الاستخدام تمكنك من إدارة متجرك دون الحاجة لخبرة تقنية معقدة
                            </p>
                        </div>
                    </div>
                    
                    <div class="feature-item" data-aos="fade-left" data-aos-delay="200">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="feature-content">
                            <h4 data-ar="تخصيص كامل" data-en="Full Customization">تخصيص كامل</h4>
                            <p data-ar="إمكانية تخصيص المنصة لتناسب احتياجات متجرك الخاصة مع خيارات متنوعة للتصميم والوظائف" data-en="Ability to customize the platform to suit your store's specific needs with various design and functionality options">
                                إمكانية تخصيص المنصة لتناسب احتياجات متجرك الخاصة مع خيارات متنوعة للتصميم والوظائف
                            </p>
                        </div>
                    </div>
                    
                    <div class="feature-item" data-aos="fade-left" data-aos-delay="300">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="feature-content">
                            <h4 data-ar="تقارير تفصيلية" data-en="Detailed Reports">تقارير تفصيلية</h4>
                            <p data-ar="تقارير شاملة ومفصلة عن المبيعات والعملاء والمخزون لمساعدتك في اتخاذ قرارات مدروسة" data-en="Comprehensive and detailed reports on sales, customers, and inventory to help you make informed decisions">
                                تقارير شاملة ومفصلة عن المبيعات والعملاء والمخزون لمساعدتك في اتخاذ قرارات مدروسة
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 data-ar="هدايا السعودية" data-en="Gifts Saudi">هدايا السعودية</h5>
                    <p data-ar="منصة ذكية لإدارة وتوصيل الهدايا في المملكة العربية السعودية" data-en="Smart platform for managing and delivering gifts in Saudi Arabia">
                        منصة ذكية لإدارة وتوصيل الهدايا في المملكة العربية السعودية
                    </p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 data-ar="روابط سريعة" data-en="Quick Links">روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="#home" data-ar="الرئيسية" data-en="Home">الرئيسية</a></li>
                        <li><a href="#services" data-ar="الخدمات" data-en="Services">الخدمات</a></li>
                        <li><a href="#features" data-ar="المميزات" data-en="Features">المميزات</a></li>
                        <li><a href="#contact" data-ar="اتصل بنا" data-en="Contact">اتصل بنا</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 data-ar="معلومات الاتصال" data-en="Contact Info">معلومات الاتصال</h5>
                    <p data-ar="الرياض، المملكة العربية السعودية" data-en="Riyadh, Saudi Arabia">الرياض، المملكة العربية السعودية</p>
                    <p>+966 50 123 4567</p>
                    <p><EMAIL></p>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 data-ar="النشرة الإخبارية" data-en="Newsletter">النشرة الإخبارية</h5>
                    <p data-ar="اشترك للحصول على آخر التحديثات" data-en="Subscribe to get latest updates">اشترك للحصول على آخر التحديثات</p>
                    <div class="newsletter-form">
                        <input type="email" class="form-control mb-2" placeholder="البريد الإلكتروني">
                        <button class="btn btn-primary w-100" data-ar="اشتراك" data-en="Subscribe">اشتراك</button>
                    </div>
                </div>
            </div>
            
            <hr style="border-color: rgba(255,255,255,0.2);">
            
            <div class="row">
                <div class="col-12 text-center">
                    <p data-ar="© 2024 هدايا السعودية. جميع الحقوق محفوظة." data-en="© 2024 Gifts Saudi. All rights reserved.">&copy; 2024 هدايا السعودية. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            easing: 'ease-in-out',
            once: true
        });

        // Language switching functionality
        function switchLanguage(lang) {
            const body = document.body;
            const langButtons = document.querySelectorAll('.lang-btn');
            const elements = document.querySelectorAll('[data-ar][data-en]');

            // Update active language button
            langButtons.forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent.includes(lang === 'ar' ? 'العربية' : 'English')) {
                    btn.classList.add('active');
                }
            });

            // Update text direction and content
            if (lang === 'ar') {
                body.className = 'rtl';
                document.documentElement.setAttribute('dir', 'rtl');
                document.documentElement.setAttribute('lang', 'ar');
            } else {
                body.className = 'ltr';
                document.documentElement.setAttribute('dir', 'ltr');
                document.documentElement.setAttribute('lang', 'en');
            }

            // Update all translatable elements
            elements.forEach(element => {
                element.textContent = element.getAttribute('data-' + lang);
            });

            // Store language preference
            localStorage.setItem('preferred-language', lang);
        }

        // Load saved language preference
        document.addEventListener('DOMContentLoaded', function() {
            const savedLang = localStorage.getItem('preferred-language') || 'ar';
            switchLanguage(savedLang);
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-custom');
            if (window.scrollY > 50) {
                navbar.style.background = 'linear-gradient(135deg, rgba(26, 0, 51, 0.95) 0%, rgba(106, 27, 154, 0.95) 100%)';
                navbar.style.backdropFilter = 'blur(10px)';
            } else {
                navbar.style.background = 'linear-gradient(135deg, var(--dark-color) 0%, var(--secondary-color) 100%)';
            }
        });
    </script>
</body>
</html> <?php /**PATH C:\hany\GiftsSaudi-Laravel (1)\GiftsSaudi-Laravel (1)\resources\views/landing.blade.php ENDPATH**/ ?>