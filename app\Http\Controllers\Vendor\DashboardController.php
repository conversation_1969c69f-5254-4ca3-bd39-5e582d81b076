<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Vendor;
use App\Models\Employee;
use App\Models\Gift;
use App\Models\GiftCampaign;
use App\Models\GiftDelivery;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the vendor dashboard.
     */
    public function index(Request $request)
    {
        $vendor = $request->vendor;
        
        // Basic Statistics
        $stats = [
            'total_employees' => $vendor->employees()->count(),
            'active_employees' => $vendor->employees()->active()->count(),
            'total_gifts' => $vendor->gifts()->count(),
            'approved_gifts' => $vendor->gifts()->approved()->count(),
            'total_campaigns' => $vendor->campaigns()->count(),
            'active_campaigns' => $vendor->campaigns()->where('gift_campaigns.stage', 'processing')->count(),
            'pending_campaigns' => $vendor->campaigns()->pendingApproval()->count(),
            'total_deliveries' => GiftDelivery::where('vendor_id', $vendor->id)->count(),
            'successful_deliveries' => GiftDelivery::where('vendor_id', $vendor->id)->delivered()->count(),
            'pending_deliveries' => GiftDelivery::where('vendor_id', $vendor->id)->pending()->count(),
        ];

        // Calculate success rate
        $stats['delivery_success_rate'] = $stats['total_deliveries'] > 0 
            ? round(($stats['successful_deliveries'] / $stats['total_deliveries']) * 100, 2) 
            : 0;

        // Recent Performance (Last 30 days)
        $thirtyDaysAgo = Carbon::now()->subDays(30);
        
        $recentStats = [
            'deliveries_this_month' => GiftDelivery::where('vendor_id', $vendor->id)
                ->where('created_at', '>=', $thirtyDaysAgo)
                ->count(),
            'successful_deliveries_this_month' => GiftDelivery::where('vendor_id', $vendor->id)
                ->delivered()
                ->where('delivered_at', '>=', $thirtyDaysAgo)
                ->count(),
            'new_employees_this_month' => $vendor->employees()
                ->where('created_at', '>=', $thirtyDaysAgo)
                ->count(),
            'campaigns_completed_this_month' => $vendor->campaigns()
                ->where('gift_campaigns.stage', 'completed')
                ->where('gift_campaigns.updated_at', '>=', $thirtyDaysAgo)
                ->count(),
        ];

        // Daily deliveries for the last 7 days (for chart)
        $dailyDeliveries = GiftDelivery::where('vendor_id', $vendor->id)
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as delivered')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Top performing employees
        $topEmployees = $vendor->employees()
            ->withCount(['deliveries', 'successfulDeliveries'])
            ->orderBy('performance_rating', 'desc')
            ->limit(5)
            ->get();

        // Recent deliveries
        $recentDeliveries = GiftDelivery::where('vendor_id', $vendor->id)
            ->with(['client', 'gift', 'employee', 'campaign'])
            ->latest()
            ->limit(10)
            ->get();

        // Upcoming deliveries
        $upcomingDeliveries = GiftDelivery::where('vendor_id', $vendor->id)
            ->pending()
            ->whereNotNull('expected_delivery_date')
            ->where('expected_delivery_date', '>=', now())
            ->with(['client', 'gift', 'employee'])
            ->orderBy('expected_delivery_date')
            ->limit(10)
            ->get();

        // Overdue deliveries
        $overdueDeliveries = GiftDelivery::where('vendor_id', $vendor->id)
            ->overdue()
            ->with(['client', 'gift', 'employee'])
            ->count();

        // Campaign performance
        $campaignStats = $vendor->campaigns()
            ->select('gift_campaigns.stage')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('gift_campaigns.stage')
            ->pluck('count', 'stage')
            ->toArray();

        return view('vendor.dashboard', compact(
            'vendor',
            'stats',
            'recentStats',
            'dailyDeliveries',
            'topEmployees',
            'recentDeliveries',
            'upcomingDeliveries',
            'overdueDeliveries',
            'campaignStats'
        ));
    }

    /**
     * Get analytics data for charts
     */
    public function analytics(Request $request)
    {
        $vendor = $request->vendor;
        $period = $request->get('period', 30); // Default 30 days

        $startDate = Carbon::now()->subDays($period);

        // Daily delivery analytics
        $dailyDeliveries = GiftDelivery::where('vendor_id', $vendor->id)
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as delivered'),
                DB::raw('SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending'),
                DB::raw('SUM(CASE WHEN status = "cancelled" THEN 1 ELSE 0 END) as cancelled')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Employee performance analytics
        $employeePerformance = $vendor->employees()
            ->with(['deliveries' => function($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
            ->get()
            ->map(function($employee) {
                $deliveries = $employee->deliveries->groupBy('status');
                return [
                    'name' => $employee->name,
                    'total' => $employee->deliveries->count(),
                    'delivered' => $deliveries->get('delivered', collect())->count(),
                    'pending' => $deliveries->get('pending', collect())->count(),
                    'cancelled' => $deliveries->get('cancelled', collect())->count(),
                    'rating' => $employee->performance_rating
                ];
            });

        // Campaign analytics
        $campaignAnalytics = $vendor->campaigns()
            ->with(['deliveries' => function($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
            ->get()
            ->map(function($campaign) {
                $deliveries = $campaign->deliveries->groupBy('status');
                return [
                    'name' => $campaign->name,
                    'total_deliveries' => $campaign->deliveries->count(),
                    'delivered' => $deliveries->get('delivered', collect())->count(),
                    'pending' => $deliveries->get('pending', collect())->count(),
                    'success_rate' => $campaign->deliveries->count() > 0 
                        ? round(($deliveries->get('delivered', collect())->count() / $campaign->deliveries->count()) * 100, 2)
                        : 0
                ];
            });

        return response()->json([
            'daily_deliveries' => $dailyDeliveries,
            'employee_performance' => $employeePerformance,
            'campaign_analytics' => $campaignAnalytics
        ]);
    }

    /**
     * Get delivery tracking information
     */
    public function trackDelivery(Request $request)
    {
        $vendor = $request->vendor;
        
        $deliveryCode = $request->get('delivery_code');
        $trackingNumber = $request->get('tracking_number');

        $query = GiftDelivery::where('vendor_id', $vendor->id);

        if ($deliveryCode) {
            $query->where('delivery_code', $deliveryCode);
        } elseif ($trackingNumber) {
            $query->where('tracking_number', $trackingNumber);
        } else {
            return response()->json(['error' => 'يرجى إدخال رقم التتبع أو رمز التسليم'], 400);
        }

        $delivery = $query->with(['client', 'gift', 'employee', 'campaign'])->first();

        if (!$delivery) {
            return response()->json(['error' => 'لم يتم العثور على الطلب'], 404);
        }

        return response()->json([
            'delivery' => $delivery,
            'timeline' => $delivery->communication_log ?? []
        ]);
    }

    /**
     * Get real-time notifications
     */
    public function notifications(Request $request)
    {
        $vendor = $request->vendor;

        $notifications = [
            'overdue_deliveries' => GiftDelivery::where('vendor_id', $vendor->id)
                ->overdue()
                ->count(),
            'new_campaign_approvals' => $vendor->campaigns()
                ->where('gift_campaigns.stage', 'approved')
                ->where('gift_campaigns.updated_at', '>=', Carbon::now()->subHours(24))
                ->count(),
            'low_performing_employees' => $vendor->employees()
                ->where('performance_rating', '<', 2.0)
                ->where('total_deliveries', '>', 5)
                ->count(),
            'pending_gifts_approval' => $vendor->gifts()
                ->where('status', 'pending')
                ->count()
        ];

        return response()->json($notifications);
    }
}
