<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Payment;
use Carbon\Carbon;

class PaymentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = ['VISA', 'MADA', 'MASTERCARD', 'APPLEPAY', null];
        $statuses = ['PAID', 'PENDING', 'FAILED', 'EXPIRED', 'CANCELLED', 'INITIATED'];
        
        $customers = [
            ['name' => 'أحمد محمد عبدالله', 'email' => '<EMAIL>', 'phone' => '+966501234567'],
            ['name' => 'فاطمة علي السعد', 'email' => '<EMAIL>', 'phone' => '+966502345678'],
            ['name' => 'محمد عبدالرحمن النجار', 'email' => '<EMAIL>', 'phone' => '+966503456789'],
            ['name' => 'نورا خالد الشهري', 'email' => '<EMAIL>', 'phone' => '+966504567890'],
            ['name' => 'عبدالله سعد المطيري', 'email' => '<EMAIL>', 'phone' => '+966505678901'],
            ['name' => 'سارة أحمد القحطاني', 'email' => '<EMAIL>', 'phone' => '+966506789012'],
            ['name' => 'يوسف محمد الغامدي', 'email' => '<EMAIL>', 'phone' => '+966507890123'],
            ['name' => 'ريم عبدالعزيز العتيبي', 'email' => '<EMAIL>', 'phone' => '+966508901234'],
            ['name' => 'خالد فهد الدوسري', 'email' => '<EMAIL>', 'phone' => '+966509012345'],
            ['name' => 'هند سليمان الحربي', 'email' => '<EMAIL>', 'phone' => '+966510123456'],
        ];

        // Generate payments for the last 90 days
        for ($i = 0; $i < 150; $i++) {
            $customer = $customers[array_rand($customers)];
            $createdAt = Carbon::now()->subDays(rand(0, 90))->subHours(rand(0, 23))->subMinutes(rand(0, 59));
            
            // Higher chance for PAID status for realistic data
            $statusWeights = [
                'PAID' => 60,
                'PENDING' => 15,
                'FAILED' => 10,
                'EXPIRED' => 5,
                'CANCELLED' => 5,
                'INITIATED' => 5
            ];
            
            $status = $this->getWeightedRandom($statusWeights);
            
            $amount = match($status) {
                'PAID' => rand(50, 5000),
                'PENDING' => rand(100, 2000),
                'FAILED' => rand(50, 1500),
                default => rand(50, 3000)
            };

            $paymentId = 'PAY_' . date('Ymd', $createdAt->timestamp) . '_' . str_pad($i + 1, 6, '0', STR_PAD_LEFT);
            $orderReference = 'ORD_' . $createdAt->format('Ymd') . '_' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            $metadata = [
                'ip_address' => '192.168.1.' . rand(1, 254),
                'user_agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
                'payment_gateway' => 'MyFatoorah',
                'gateway_reference' => 'GW_' . $paymentId,
                'currency' => 'SAR',
                'locale' => 'ar'
            ];

            if ($status === 'PAID') {
                $metadata['payment_date'] = $createdAt->addMinutes(rand(1, 30))->toISOString();
                $metadata['authorization_code'] = 'AUTH_' . rand(100000, 999999);
            }

            Payment::create([
                'amount' => $amount,
                'order_reference' => $orderReference,
                'customer_name' => $customer['name'],
                'customer_email' => $customer['email'],
                'customer_phone' => $customer['phone'],
                'payment_id' => $paymentId,
                'transaction_id' => $status === 'PAID' ? 'TXN_' . rand(1000000000, 9999999999) : null,
                'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                'payment_status' => $status,
                'metadata' => $metadata,
                'test_mode' => rand(0, 10) > 8, // 20% test mode
                'created_at' => $createdAt,
                'updated_at' => $status === 'PAID' ? $createdAt->addMinutes(rand(1, 30)) : $createdAt
            ]);
        }

        // Add some high-value payments for testing
        $highValueCustomers = [
            ['name' => 'شركة الرياض للتجارة', 'email' => '<EMAIL>', 'phone' => '+966112345678'],
            ['name' => 'مؤسسة جدة للاستثمار', 'email' => '<EMAIL>', 'phone' => '+966122345678'],
            ['name' => 'شركة الشرقية للتطوير', 'email' => '<EMAIL>', 'phone' => '+966132345678'],
        ];

        foreach ($highValueCustomers as $customer) {
            for ($j = 0; $j < 3; $j++) {
                $createdAt = Carbon::now()->subDays(rand(1, 30));
                $amount = rand(10000, 50000);
                $paymentId = 'PAY_CORP_' . date('Ymd', $createdAt->timestamp) . '_' . str_pad($j + 1, 3, '0', STR_PAD_LEFT);
                
                Payment::create([
                    'amount' => $amount,
                    'order_reference' => 'CORP_ORD_' . $createdAt->format('Ymd') . '_' . str_pad($j + 1, 3, '0', STR_PAD_LEFT),
                    'customer_name' => $customer['name'],
                    'customer_email' => $customer['email'],
                    'customer_phone' => $customer['phone'],
                    'payment_id' => $paymentId,
                    'transaction_id' => 'TXN_CORP_' . rand(1000000000, 9999999999),
                    'payment_method' => 'VISA',
                    'payment_status' => 'PAID',
                    'metadata' => [
                        'ip_address' => '192.168.1.' . rand(1, 254),
                        'user_agent' => 'Corporate Payment System',
                        'payment_gateway' => 'MyFatoorah',
                        'gateway_reference' => 'GW_CORP_' . $paymentId,
                        'currency' => 'SAR',
                        'locale' => 'ar',
                        'payment_type' => 'corporate',
                        'payment_date' => $createdAt->addMinutes(rand(1, 10))->toISOString(),
                        'authorization_code' => 'AUTH_CORP_' . rand(100000, 999999)
                    ],
                    'test_mode' => false,
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt->addMinutes(rand(1, 10))
                ]);
            }
        }
    }

    private function getWeightedRandom($weights)
    {
        $totalWeight = array_sum($weights);
        $randomNumber = rand(1, $totalWeight);
        
        $weightSum = 0;
        foreach ($weights as $option => $weight) {
            $weightSum += $weight;
            if ($randomNumber <= $weightSum) {
                return $option;
            }
        }
        
        return array_key_first($weights);
    }
}
