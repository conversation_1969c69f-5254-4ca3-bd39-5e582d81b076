<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MessageTemplate extends Model
{
    protected $fillable = [
        'name',
        'title',
        'content',
        'type',
        'is_active',
        'variables',
        'image_url',
        'media_type',
        'media_path',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'variables' => 'array',
    ];

    // Relationships
    public function giftCampaigns(): HasMany
    {
        return $this->hasMany(GiftCampaign::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Helper methods
    public function getPreviewContent(array $variables = []): string
    {
        $content = $this->content;
        
        if (!empty($variables)) {
            foreach ($variables as $key => $value) {
                $content = str_replace("{{$key}}", $value, $content);
            }
        }
        
        return $content;
    }

    public function hasVariable(string $variable): bool
    {
        return in_array($variable, $this->variables ?? []);
    }

    public function getVariablesList(): array
    {
        return $this->variables ?? [];
    }

    public function getTypeDisplayName(): string
    {
        return match($this->type) {
            'welcome' => 'رسائل ترحيب',
            'gift_notification' => 'إشعارات الهدايا',
            'payment_reminder' => 'تذكير دفع',
            'custom' => 'مخصص',
            default => 'عام',
        };
    }

    public function getMediaTypeDisplayName(): string
    {
        return match($this->media_type) {
            'image' => 'صورة',
            'video' => 'فيديو',
            'audio' => 'صوت',
            default => 'بدون وسائط',
        };
    }
}
