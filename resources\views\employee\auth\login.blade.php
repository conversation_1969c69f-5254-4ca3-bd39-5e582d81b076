<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>تسجيل دخول الموظف - هدايا السعودية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --employee-primary: #28a745;
            --employee-secondary: #17a2b8;
            --employee-accent: #ffc107;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--employee-primary) 0%, #20c997 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--employee-primary) 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }

        .login-title {
            color: #343a40;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--employee-primary);
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .input-group {
            position: relative;
        }

        .input-group-text {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            z-index: 10;
        }

        .form-control-with-icon {
            padding-left: 3rem;
        }

        .btn-login {
            width: 100%;
            padding: 1rem;
            font-size: 1rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--employee-primary) 0%, #20c997 100%);
            border: none;
            border-radius: 12px;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .alert-mobile {
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: none;
        }

        .alert-danger-mobile {
            background: rgba(220, 53, 69, 0.1);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .alert-success-mobile {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border-left: 4px solid var(--employee-primary);
        }

        .form-check {
            margin: 1rem 0;
        }

        .form-check-input:checked {
            background-color: var(--employee-primary);
            border-color: var(--employee-primary);
        }

        .form-check-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .loading-spinner {
            display: none;
        }

        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            .login-container {
                padding: 1.5rem;
                margin: 0.5rem;
            }

            .login-logo {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .login-title {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-logo">
                <i class="fas fa-user-tie"></i>
            </div>
            <h2 class="login-title">تسجيل دخول الموظف</h2>
            <p class="login-subtitle">أدخل بيانات الدخول للوصول إلى لوحة التحكم</p>
        </div>

        @if(session('success'))
            <div class="alert-mobile alert-success-mobile">
                <i class="fas fa-check-circle me-2"></i>
                {{ session('success') }}
            </div>
        @endif

        @if($errors->any())
            <div class="alert-mobile alert-danger-mobile">
                <i class="fas fa-exclamation-circle me-2"></i>
                @foreach($errors->all() as $error)
                    <div>{{ $error }}</div>
                @endforeach
            </div>
        @endif

        <form method="POST" action="{{ route('employee.login.submit') }}" id="loginForm">
            @csrf
            
            <div class="form-group">
                <label for="employee_id" class="form-label">رقم الموظف أو رقم الجوال</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-id-card"></i>
                    </span>
                    <input type="text" 
                           class="form-control form-control-with-icon" 
                           id="employee_id" 
                           name="employee_id" 
                           value="{{ old('employee_id') }}"
                           placeholder="أدخل رقم الموظف أو رقم الجوال"
                           required 
                           autocomplete="username">
                </div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">كلمة المرور</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" 
                           class="form-control form-control-with-icon" 
                           id="password" 
                           name="password" 
                           placeholder="أدخل كلمة المرور"
                           required 
                           autocomplete="current-password">
                </div>
            </div>

            <div class="form-check">
                <input class="form-check-input" 
                       type="checkbox" 
                       id="remember" 
                       name="remember">
                <label class="form-check-label" for="remember">
                    تذكرني
                </label>
            </div>

            <button type="submit" class="btn btn-login" id="loginBtn">
                <span class="btn-text">تسجيل الدخول</span>
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري التحقق...
                </div>
            </button>
        </form>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const btn = document.getElementById('loginBtn');
            const btnText = btn.querySelector('.btn-text');
            const spinner = btn.querySelector('.loading-spinner');
            
            btn.disabled = true;
            btnText.style.display = 'none';
            spinner.style.display = 'inline-block';
        });

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert-mobile');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            });
        });
    </script>
</body>
</html> 