<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gift_deliveries', function (Blueprint $table) {
            $table->id();
            
            // Relationships
            $table->foreignId('campaign_id')->constrained('gift_campaigns')->onDelete('cascade');
            $table->foreignId('client_id')->constrained('clients')->onDelete('cascade');
            $table->foreignId('gift_id')->constrained('gifts')->onDelete('cascade');
            $table->foreignId('employee_id')->constrained('employees')->onDelete('cascade');
            $table->foreignId('vendor_id')->constrained('vendors')->onDelete('cascade');
            
            // Delivery Information
            $table->string('delivery_code')->unique(); // QR/Barcode for gift
            $table->enum('status', ['pending', 'delivered', 'cancelled', 'expired'])->default('pending');
            $table->timestamp('delivered_at')->nullable();
            $table->text('delivery_notes')->nullable();
            
            // Location & Verification
            $table->string('delivery_address')->nullable();
            $table->decimal('delivery_latitude', 10, 8)->nullable();
            $table->decimal('delivery_longitude', 11, 8)->nullable();
            $table->string('client_signature')->nullable(); // Path to signature image
            $table->string('delivery_photo')->nullable(); // Photo proof of delivery
            
            // Client Experience
            $table->integer('client_rating')->nullable(); // 1-5 rating
            $table->text('client_feedback')->nullable();
            $table->boolean('client_satisfied')->nullable();
            
            // System Tracking
            $table->string('tracking_number')->unique();
            $table->timestamp('expected_delivery_date')->nullable();
            $table->integer('delivery_attempts')->default(0);
            $table->text('delivery_failure_reason')->nullable();
            
            // Communication Log
            $table->json('communication_log')->nullable(); // SMS/WhatsApp logs
            $table->timestamp('last_contact_at')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['campaign_id', 'status']);
            $table->index(['employee_id', 'delivered_at']);
            $table->index(['vendor_id', 'status']);
            $table->index('delivery_code');
            $table->index('tracking_number');
            $table->index('delivered_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gift_deliveries');
    }
};
