<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            
            // Order details
            $table->decimal('amount', 10, 2);
            $table->string('order_reference')->index();
            
            // Customer details
            $table->string('customer_name');
            $table->string('customer_email');
            $table->string('customer_phone');
            
            // Payment details
            $table->string('payment_id')->unique()->index();
            $table->string('transaction_id')->nullable();
            $table->string('payment_method')->nullable();
            $table->enum('payment_status', [
                'INITIATED', 
                'PENDING', 
                'PAID', 
                'FAILED', 
                'EXPIRED', 
                'CANCELLED'
            ])->default('INITIATED');
            
            // Additional data
            $table->json('metadata')->nullable();
            $table->boolean('test_mode')->default(true);
            
            $table->timestamps();
            
            // Indexes
            $table->index('payment_status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
