<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Vendor extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'user_id',
        'username',
        'company_name',
        'email',
        'phone_number',
        'address',
        'website',
        'category',
        'is_approved',
        'facebook',
        'instagram',
        'twitter',
        'bank_name',
        'account_name',
        'account_number',
        'iban',
        'description',
        'logo_url',
        'password',
        'email_verified_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'is_approved' => 'boolean',
        'password' => 'hashed',
        'email_verified_at' => 'datetime',
    ];

    /**
     * Get the user that owns the vendor
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all gifts for this vendor
     */
    public function gifts(): HasMany
    {
        return $this->hasMany(Gift::class);
    }

    /**
     * Get approved gifts for this vendor
     */
    public function approvedGifts(): HasMany
    {
        return $this->hasMany(Gift::class)->where('approved', true);
    }

    /**
     * Get vendor finances
     */
    public function finances(): HasMany
    {
        return $this->hasMany(VendorFinance::class);
    }

    /**
     * Get all employees for this vendor
     */
    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class);
    }

    /**
     * Get all campaigns for this vendor
     */
    public function campaigns(): HasManyThrough
    {
        return $this->hasManyThrough(GiftCampaign::class, Gift::class);
    }

    /**
     * Check if vendor is approved
     */
    public function isApproved(): bool
    {
        return $this->is_approved;
    }

    /**
     * Get the vendor's social media links
     */
    public function getSocialMediaAttribute(): array
    {
        return [
            'facebook' => $this->facebook,
            'instagram' => $this->instagram,
            'twitter' => $this->twitter,
        ];
    }

    /**
     * Get the vendor's bank info
     */
    public function getBankInfoAttribute(): array
    {
        return [
            'bank_name' => $this->bank_name,
            'account_name' => $this->account_name,
            'account_number' => $this->account_number,
            'iban' => $this->iban,
        ];
    }

    /**
     * Scope for approved vendors
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope for pending vendors
     */
    public function scopePending($query)
    {
        return $query->where('is_approved', false);
    }
}
