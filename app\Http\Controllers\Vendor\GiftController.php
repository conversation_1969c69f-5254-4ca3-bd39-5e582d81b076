<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Gift;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class GiftController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $vendor = Auth::user()->vendor;
        $gifts = Gift::where('vendor_id', $vendor->id)
                    ->orderBy('created_at', 'desc')
                    ->paginate(12);

        return view('vendor.gifts.index', compact('gifts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('vendor.gifts.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'category' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120' // 5MB max
        ]);

        $vendor = Auth::user()->vendor;

        $giftData = [
            'vendor_id' => $vendor->id,
            'name' => $request->name,
            'description' => $request->description,
            'price' => $request->price,
            'stock_quantity' => $request->stock_quantity,
            'category' => $request->category,
            'approval_status' => 'pending',
            'is_active' => false
        ];

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('gifts', 'public');
            $giftData['image'] = $imagePath;
        }

        Gift::create($giftData);

        return redirect()->route('vendor.gifts.index')
                        ->with('success', 'تم إضافة الهدية بنجاح! في انتظار موافقة الإدارة.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Gift $gift)
    {
        // Make sure this gift belongs to the current vendor
        $vendor = Auth::user()->vendor;
        if ($gift->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بعرض هذه الهدية');
        }

        return view('vendor.gifts.show', compact('gift'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Gift $gift)
    {
        // Make sure this gift belongs to the current vendor
        $vendor = Auth::user()->vendor;
        if ($gift->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بتعديل هذه الهدية');
        }

        return view('vendor.gifts.edit', compact('gift'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Gift $gift)
    {
        // Make sure this gift belongs to the current vendor
        $vendor = Auth::user()->vendor;
        if ($gift->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بتعديل هذه الهدية');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'category' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120'
        ]);

        $giftData = [
            'name' => $request->name,
            'description' => $request->description,
            'price' => $request->price,
            'stock_quantity' => $request->stock_quantity,
            'category' => $request->category
        ];

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($gift->image) {
                Storage::disk('public')->delete($gift->image);
            }
            
            $imagePath = $request->file('image')->store('gifts', 'public');
            $giftData['image'] = $imagePath;
        }

        // Reset approval status if gift was modified
        if ($gift->approval_status === 'approved') {
            $giftData['approval_status'] = 'pending';
            $giftData['is_active'] = false;
        }

        $gift->update($giftData);

        return redirect()->route('vendor.gifts.index')
                        ->with('success', 'تم تحديث الهدية بنجاح!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Gift $gift)
    {
        // Make sure this gift belongs to the current vendor
        $vendor = Auth::user()->vendor;
        if ($gift->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بحذف هذه الهدية');
        }

        // Delete image if exists
        if ($gift->image) {
            Storage::disk('public')->delete($gift->image);
        }

        $gift->delete();

        return redirect()->route('vendor.gifts.index')
                        ->with('success', 'تم حذف الهدية بنجاح!');
    }

    /**
     * Submit gift for approval
     */
    public function submitForApproval(Gift $gift)
    {
        // Make sure this gift belongs to the current vendor
        $vendor = Auth::user()->vendor;
        if ($gift->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بإرسال هذه الهدية للموافقة');
        }

        $gift->update([
            'approval_status' => 'pending'
        ]);

        return redirect()->back()
                        ->with('success', 'تم إرسال الهدية للموافقة بنجاح!');
    }

    /**
     * Update stock quantity
     */
    public function updateStock(Request $request, Gift $gift)
    {
        $request->validate([
            'stock_quantity' => 'required|integer|min:0'
        ]);

        // Make sure this gift belongs to the current vendor
        $vendor = Auth::user()->vendor;
        if ($gift->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بتحديث مخزون هذه الهدية');
        }

        $gift->update([
            'stock_quantity' => $request->stock_quantity
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث المخزون بنجاح'
        ]);
    }
}
