@extends('layouts.vendor')

@section('title', 'جميع التسليمات')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">
                <i class="fas fa-truck me-2"></i>
                جميع التسليمات
            </h1>
            <p class="text-muted">
                إدارة ومتابعة جميع التسليمات
            </p>
        </div>
    </div>

    <!-- Stats Row -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ $stats['total'] }}</h3>
                    <p class="mb-0">إجمالي التسليمات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ $stats['delivered'] }}</h3>
                    <p class="mb-0">تم التسليم</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ $stats['pending'] }}</h3>
                    <p class="mb-0">في الانتظار</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ $stats['cancelled'] }}</h3>
                    <p class="mb-0">ملغي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Deliveries Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">قائمة التسليمات</h5>
        </div>
        <div class="card-body">
            @if($deliveries->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>الحملة</th>
                                <th>الهدية</th>
                                <th>الموظف</th>
                                <th>الحالة</th>
                                <th>تاريخ التوقع</th>
                                <th>تاريخ التسليم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($deliveries as $delivery)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $delivery->client->name ?? 'غير محدد' }}</strong><br>
                                            <small class="text-muted">{{ $delivery->client->phone ?? '' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $delivery->campaign->name ?? 'غير محدد' }}</strong><br>
                                            <small class="text-muted">{{ $delivery->campaign->created_at ? $delivery->campaign->created_at->format('d/m/Y') : '' }}</small>
                                        </div>
                                    </td>
                                    <td>{{ $delivery->gift->name ?? 'غير محدد' }}</td>
                                    <td>
                                        @if($delivery->employee)
                                            <span class="badge bg-info">{{ $delivery->employee->name }}</span>
                                        @else
                                            <span class="badge bg-secondary">غير مخصص</span>
                                        @endif
                                    </td>
                                    <td>
                                        @switch($delivery->status)
                                            @case('pending')
                                                <span class="badge bg-warning">في الانتظار</span>
                                                @break
                                            @case('delivered')
                                                <span class="badge bg-success">تم التسليم</span>
                                                @break
                                            @case('cancelled')
                                                <span class="badge bg-danger">ملغي</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ $delivery->status }}</span>
                                        @endswitch
                                    </td>
                                    <td>
                                        {{ $delivery->expected_delivery_date ? \Carbon\Carbon::parse($delivery->expected_delivery_date)->format('d/m/Y') : 'غير محدد' }}
                                    </td>
                                    <td>
                                        {{ $delivery->delivered_at ? \Carbon\Carbon::parse($delivery->delivered_at)->format('d/m/Y H:i') : '-' }}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            @if($delivery->status === 'pending')
                                                <button type="button" class="btn btn-sm btn-success" 
                                                        onclick="markDelivered({{ $delivery->id }})"
                                                        title="تأكيد التسليم">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            @endif
                                            
                                            @if(!$delivery->employee_id)
                                                <button type="button" class="btn btn-sm btn-primary" 
                                                        onclick="assignEmployee({{ $delivery->id }})"
                                                        title="تخصيص موظف">
                                                    <i class="fas fa-user-plus"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $deliveries->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد تسليمات</h5>
                    <p class="text-muted">لم يتم العثور على أي تسليمات حتى الآن</p>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Assign Employee Modal -->
<div class="modal fade" id="assignEmployeeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تخصيص موظف للتسليم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="assignEmployeeForm">
                <div class="modal-body">
                    <input type="hidden" id="deliveryId" name="delivery_id">
                    
                    <div class="mb-3">
                        <label for="employee_id" class="form-label">الموظف</label>
                        <select class="form-select" id="employee_id" name="employee_id" required>
                            <option value="">اختر موظف</option>
                            <!-- Will be populated via AJAX -->
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="expected_delivery_date" class="form-label">تاريخ التسليم المتوقع</label>
                        <input type="date" class="form-control" id="expected_delivery_date" name="expected_delivery_date">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تخصيص</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function markDelivered(deliveryId) {
    if (confirm('هل أنت متأكد من تأكيد التسليم؟')) {
        fetch(`/vendor/deliveries/${deliveryId}/mark-delivered`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function assignEmployee(deliveryId) {
    document.getElementById('deliveryId').value = deliveryId;
    
    // Load employees
    fetch('/vendor/employees/list')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('employee_id');
            select.innerHTML = '<option value="">اختر موظف</option>';
            data.forEach(employee => {
                select.innerHTML += `<option value="${employee.id}">${employee.name}</option>`;
            });
        });
    
    new bootstrap.Modal(document.getElementById('assignEmployeeModal')).show();
}

document.getElementById('assignEmployeeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const deliveryId = formData.get('delivery_id');
    
    fetch(`/vendor/deliveries/${deliveryId}/assign-employee`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
});
</script>
@endsection
