<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to recreate the table with the new enum values
        Schema::table('employees', function (Blueprint $table) {
            // Drop the index first
            $table->dropIndex(['vendor_id', 'status']);
        });

        Schema::table('employees', function (Blueprint $table) {
            // Drop the existing status column
            $table->dropColumn('status');
        });

        Schema::table('employees', function (Blueprint $table) {
            // Add the new status column with pending included
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending'])->default('active')->after('address');
            // Recreate the index
            $table->index(['vendor_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            // Drop the index first
            $table->dropIndex(['vendor_id', 'status']);
        });

        Schema::table('employees', function (Blueprint $table) {
            // Drop the new status column
            $table->dropColumn('status');
        });

        Schema::table('employees', function (Blueprint $table) {
            // Restore the original status column
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active')->after('address');
            // Recreate the index
            $table->index(['vendor_id', 'status']);
        });
    }
};
