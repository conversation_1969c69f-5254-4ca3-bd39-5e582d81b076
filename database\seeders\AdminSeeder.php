<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Admin;
use App\Models\Vendor;
use App\Models\Client;
use App\Models\Gift;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Super Admin
        $superAdminUser = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin',
            'phone' => '+966501234567',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        Admin::create([
            'user_id' => $superAdminUser->id,
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'level' => 'super_admin',
            'permissions' => json_encode([
                'manage_users', 'manage_vendors', 'manage_clients', 
                'manage_gifts', 'manage_payments', 'view_analytics'
            ]),
        ]);

        // Create Sample Vendors
        for ($i = 1; $i <= 5; $i++) {
            $vendorUser = User::create([
                'name' => "تاجر نموذجي {$i}",
                'email' => "vendor{$i}@example.com",
                'password' => Hash::make('password123'),
                'role' => 'vendor',
                'phone' => '+96650' . str_pad($i, 7, '0', STR_PAD_LEFT),
                'status' => 'active',
                'email_verified_at' => now(),
            ]);

            $vendor = Vendor::create([
                'user_id' => $vendorUser->id,
                'username' => "vendor{$i}",
                'company_name' => "شركة الهدايا الرائعة {$i}",
                'email' => "vendor{$i}@example.com",
                'phone_number' => '+96650' . str_pad($i, 7, '0', STR_PAD_LEFT),
                'address' => "الرياض، المملكة العربية السعودية {$i}",
                'website' => "https://vendor{$i}.example.com",
                'category' => $this->getRandomCategory(),
                'description' => "وصف تفصيلي للتاجر رقم {$i} وخدماته المميزة",
                'is_approved' => $i <= 3, // First 3 vendors are approved
                'bank_name' => 'البنك الأهلي السعودي',
                'account_name' => "شركة الهدايا الرائعة {$i}",
                'account_number' => '*********' . $i,
                'iban' => 'SA' . str_pad($i, 22, '0', STR_PAD_LEFT),
            ]);

            // Create gifts for each vendor
            for ($j = 1; $j <= rand(3, 8); $j++) {
                Gift::create([
                    'vendor_id' => $vendor->id,
                    'name' => $this->getRandomGiftName() . " {$j}",
                    'description' => 'وصف تفصيلي للهدية مع مميزاتها الفريدة',
                    'price' => rand(50, 500),
                    'category' => $this->getRandomGiftCategory(),
                    'status' => $this->getRandomStatus(),
                    'approved' => rand(0, 1),
                    'notes' => 'ملاحظات إضافية حول الهدية',
                ]);
            }
        }

        // Create Sample Clients
        for ($i = 1; $i <= 20; $i++) {
            $gift = Gift::inRandomOrder()->first();
            
            Client::create([
                'name' => $this->getRandomArabicName(),
                'phone' => '+96650' . str_pad($i, 7, '0', STR_PAD_LEFT),
                'email' => "client{$i}@example.com",
                'country' => 'المملكة العربية السعودية',
                'city' => $this->getRandomSaudiCity(),
                'neighborhood' => $this->getRandomNeighborhood(),
                'gender' => rand(0, 1) ? 'Male' : 'Female',
                'age' => rand(18, 65),
                'health_condition' => rand(0, 1) ? 'Healthy' : 'Chronic',
                'birthdate' => now()->subYears(rand(18, 65))->subDays(rand(1, 365)),
                'occupation' => $this->getRandomOccupation(),
                'gift_id' => $gift?->id,
                'message_received' => rand(0, 1),
                'message_sent_at' => rand(0, 1) ? now()->subDays(rand(1, 30)) : null,
                'gift_received' => rand(0, 1),
                'gift_received_at' => rand(0, 1) ? now()->subDays(rand(1, 15)) : null,
                'rating' => rand(0, 1) ? rand(3, 5) : null,
                'feedback' => rand(0, 1) ? 'تجربة رائعة وخدمة ممتازة' : null,
                'amount_paid' => $gift ? $gift->price : rand(100, 300),
                'payment_method' => $this->getRandomPaymentMethod(),
                'payment_status' => rand(0, 1) ? 'paid' : 'unpaid',
                'assigned_employee' => rand(0, 1) ? 'موظف الخدمة ' . rand(1, 5) : null,
                'additional_fields' => json_encode([
                    'notes' => 'ملاحظات إضافية للعميل',
                    'source' => $this->getRandomSource(),
                ]),
            ]);
        }

        $this->command->info('تم إنشاء البيانات النموذجية بنجاح!');
        $this->command->info('معلومات الدخول للمدير:');
        $this->command->info('البريد الإلكتروني: <EMAIL>');
        $this->command->info('كلمة المرور: password123');
    }

    private function getRandomCategory(): string
    {
        $categories = ['إلكترونيات', 'أزياء', 'منزل وحديقة', 'رياضة', 'كتب', 'مجوهرات', 'عطور'];
        return $categories[array_rand($categories)];
    }

    private function getRandomGiftName(): string
    {
        $gifts = [
            'هدية فاخرة', 'صندوق هدايا', 'باقة ورود', 'عطر فاخر', 
            'ساعة يد', 'مجوهرات', 'شوكولاتة فاخرة', 'كتاب مميز'
        ];
        return $gifts[array_rand($gifts)];
    }

    private function getRandomStatus(): string
    {
        $statuses = ['قيد الانتظار', 'تم التسليم', 'تم الرفض'];
        return $statuses[array_rand($statuses)];
    }

    private function getRandomArabicName(): string
    {
        $names = [
            'أحمد محمد', 'فاطمة علي', 'عبدالله سالم', 'نورا أحمد', 'محمد عبدالله',
            'عائشة سعد', 'عبدالرحمن محمد', 'سارة علي', 'يوسف أحمد', 'زينب محمد'
        ];
        return $names[array_rand($names)];
    }

    private function getRandomSaudiCity(): string
    {
        $cities = ['الرياض', 'جدة', 'مكة المكرمة', 'المدينة المنورة', 'الدمام', 'تبوك', 'أبها', 'القصيم'];
        return $cities[array_rand($cities)];
    }

    private function getRandomNeighborhood(): string
    {
        $neighborhoods = ['العليا', 'الملز', 'النخيل', 'الروضة', 'الياسمين', 'الورود', 'السلام', 'النهضة'];
        return $neighborhoods[array_rand($neighborhoods)];
    }

    private function getRandomHealthCondition(): string
    {
        $conditions = ['سليم', 'سكري', 'ضغط', 'قلب', 'ربو', 'حساسية'];
        return $conditions[array_rand($conditions)];
    }

    private function getRandomOccupation(): string
    {
        $occupations = ['مهندس', 'طبيب', 'معلم', 'محاسب', 'مبرمج', 'بائع', 'طالب', 'ربة منزل'];
        return $occupations[array_rand($occupations)];
    }

    private function getRandomPaymentMethod(): string
    {
        $methods = ['فيزا', 'ماستركارد', 'مدى', 'أبل باي', 'تحويل بنكي', 'نقدي'];
        return $methods[array_rand($methods)];
    }

    private function getRandomPaymentStatus(): string
    {
        $statuses = ['pending', 'paid', 'failed', 'refunded'];
        return $statuses[array_rand($statuses)];
    }

    private function getRandomSource(): string
    {
        $sources = ['موقع إلكتروني', 'فيسبوك', 'انستقرام', 'تويتر', 'واتساب', 'إحالة صديق'];
        return $sources[array_rand($sources)];
    }

    private function getRandomGiftCategory(): string
    {
        $categories = ['هدايا شخصية', 'هدايا أطفال', 'هدايا أعياد', 'زهور', 'حلويات', 'إلكترونيات', 'أخرى'];
        return $categories[array_rand($categories)];
    }
}
