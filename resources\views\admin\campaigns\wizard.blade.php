@extends('layouts.admin')

@section('title', 'معالج إنشاء الحملة - ' . $campaign->getStageDisplayName())

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-magic me-2"></i>
        معالج إنشاء الحملة
    </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.campaigns.index') }}">الحملات الإعلانية</a></li>
                        <li class="breadcrumb-item active" aria-current="page">معالج الحملة</li>
                    </ol>
                </nav>
            </div>
    </div>
</div>

    <div class="row justify-content-center">
        <div class="col-lg-10">
<div class="wizard-container">
                <div class="wizard-header">
                    <h1>
                        <i class="fas fa-{{ $campaign->stage === 'gift_info' ? 'gift' : ($campaign->stage === 'template_selection' ? 'clipboard-list' : ($campaign->stage === 'client_filtering' ? 'filter' : ($campaign->stage === 'message_count' ? 'envelope' : ($campaign->stage === 'platform_selection' ? 'server' : 'credit-card')))) }} me-2"></i>
                        {{ $campaign->getStageDisplayName() }}
                    </h1>
                    <p>المرحلة {{ $campaign->getProgressPercentage() }}% من إنشاء الحملة الإعلانية</p>
                </div>

                <div class="wizard-body">
                    <!-- Progress Bar -->
                    <div class="progress mb-4">
                        <div class="progress-bar bg-primary" style="width: {{ $campaign->getProgressPercentage() }}%"></div>
                    </div>

                    <!-- Step Indicator -->
                    <div class="step-indicator mb-4">
                        @php
                            $stages = [
                                'gift_info' => '1',
                                'template_selection' => '2', 
                                'client_filtering' => '3',
                                'message_count' => '4',
                                'platform_selection' => '5',
                                'payment' => '6',
                            ];
                            
                            $stageOrder = ['gift_info', 'template_selection', 'client_filtering', 'message_count', 'platform_selection', 'payment'];
                            $currentStageIndex = array_search($campaign->stage, $stageOrder);
                        @endphp
                        
                        @foreach($stageOrder as $index => $stage)
                            <div class="step {{ $index < $currentStageIndex ? 'completed' : ($index == $currentStageIndex ? 'active' : '') }}">
                                {{ $index < $currentStageIndex ? '✓' : $stages[$stage] }}
                            </div>
                        @endforeach
                    </div>

    <!-- Stage Content -->
    <div class="stage-content">
        <form action="{{ route('admin.campaigns.update-stage', $campaign) }}" method="POST" enctype="multipart/form-data">
            @csrf
            
            <!-- Error Display -->
            @if ($errors->any())
                <div class="alert alert-danger mb-4">
                    <h4><i class="fas fa-exclamation-triangle me-2"></i>حدثت أخطاء في التحقق:</h4>
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            
            <!-- Success Message -->
            @if (session('success'))
                <div class="alert alert-success mb-4">
                    <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                </div>
            @endif
            
            @if($campaign->stage === 'gift_info')
                @include('admin.campaigns.stages.gift-info')
            @elseif($campaign->stage === 'template_selection')
                @include('admin.campaigns.stages.template-selection')
            @elseif($campaign->stage === 'client_filtering')
                @include('admin.campaigns.stages.client-filtering')
            @elseif($campaign->stage === 'message_count')
                @include('admin.campaigns.stages.message-count')
            @elseif($campaign->stage === 'platform_selection')
                @include('admin.campaigns.stages.platform-selection')
            @elseif($campaign->stage === 'payment')
                @include('admin.campaigns.stages.payment')
            @else
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h4 class="text-white">تم إكمال هذه المرحلة</h4>
                    <p class="text-muted">المرحلة الحالية: {{ $campaign->getStageDisplayName() }}</p>
                    <a href="{{ route('admin.campaigns.show', $campaign) }}" class="btn btn-gradient">
                        <i class="fas fa-eye me-2"></i>
                        عرض تفاصيل الحملة
                    </a>
                </div>
            @endif

                    @if(in_array($campaign->stage, ['gift_info', 'template_selection', 'client_filtering', 'message_count', 'platform_selection', 'payment']))
                        <div class="wizard-navigation">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    @if($campaign->stage !== 'gift_info')
                                        <form action="{{ route('admin.campaigns.go-back-stage', $campaign) }}" method="POST" style="display: inline;">
                                            @csrf
                                            <button type="submit" class="btn btn-outline-secondary">
                                                <i class="fas fa-arrow-right me-2"></i>
                                                المرحلة السابقة
                                            </button>
                                        </form>
                                    @else
                                        <a href="{{ route('admin.campaigns.index') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-right me-2"></i>
                                            العودة للحملات
                                        </a>
                                    @endif
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    @if($campaign->stage === 'payment')
                                        إكمال الحملة
                                    @else
                                        التالي
                                    @endif
                                </button>
                            </div>
                        </div>
                    @endif
                </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    margin-bottom: 2rem;
    text-align: center;
}

.page-title {
    color: #333;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.wizard-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    position: relative;
}

.wizard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.wizard-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 30px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
    position: relative;
}

.wizard-header h1 {
    color: #667eea;
    font-weight: 700;
    font-size: 2rem;
    margin-bottom: 10px;
}

.wizard-header p {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
}

.wizard-body {
    padding: 40px;
    color: #212529;
}

.step-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.step {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 10px;
    position: relative;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.step.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    transform: scale(1.2);
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
}

.step.completed {
    background: #28a745;
    color: white;
}

.step::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -12px;
    width: 14px;
    height: 2px;
    background: #e9ecef;
    transition: all 0.3s ease;
}

.step.completed::after {
    background: #28a745;
}

.step:last-child::after {
    display: none;
}

.stage-content {
    position: relative;
    min-height: 400px;
}

.form-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.section-title {
    color: #333;
    font-weight: 600;
    font-size: 1.3rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-label {
    color: #495057;
    font-weight: 600;
    margin-bottom: 10px;
}

.form-control, .form-select {
    background: white;
    border: 2px solid #e9ecef;
    color: #495057;
    border-radius: 10px;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    background: white;
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    color: #495057;
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.wizard-navigation {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.template-option {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.template-option:hover {
    border-color: #667eea;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.template-option.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
}

.client-count-display {
    background: white;
    border: 2px solid #667eea;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    margin: 20px 0;
}

.client-count-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.custom-media-upload {
    border: 2px dashed #e9ecef;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.custom-media-upload:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.summary-card {
    background: white;
    border: 2px solid #667eea;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-label {
    color: #6c757d;
}

.summary-value {
    color: #667eea;
    font-weight: bold;
}
</style>

@endsection

@push('scripts')
<script>
// Client filtering is handled in the individual stage template

// Template selection
document.querySelectorAll('.template-option').forEach(option => {
    option.addEventListener('click', function() {
        document.querySelectorAll('.template-option').forEach(opt => opt.classList.remove('selected'));
        this.classList.add('selected');
        
        const templateId = this.getAttribute('data-template-id');
        const templateType = this.getAttribute('data-template-type');
        
        document.querySelector('[name="template_type"]').value = templateType;
        if (templateType === 'prebuilt') {
            document.querySelector('[name="message_template_id"]').value = templateId;
        }
    });
});

// Custom media upload
document.getElementById('customMediaUpload')?.addEventListener('click', function() {
    document.getElementById('customMediaFile').click();
});

document.getElementById('customMediaFile')?.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const uploadArea = document.getElementById('customMediaUpload');
        uploadArea.innerHTML = `
            <i class="fas fa-file-upload fa-2x text-success mb-2"></i>
            <p class="text-success mb-0">تم اختيار الملف: ${file.name}</p>
        `;
    }
});

// Platform selection
document.querySelectorAll('[name="platform"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const tokenField = document.getElementById('whatsappTokenField');
        if (this.value === 'external') {
            tokenField.style.display = 'block';
        } else {
            tokenField.style.display = 'none';
        }
    });
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Client filtering is handled in the individual stage template
    console.log('Campaign wizard initialized');
});
</script>
@endpush 