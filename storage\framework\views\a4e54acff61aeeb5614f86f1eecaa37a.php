

<?php $__env->startSection('title', 'تفاصيل التاجر'); ?>
<?php $__env->startSection('page-title', 'تفاصيل التاجر'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .detail-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        margin-bottom: 30px;
    }

    .vendor-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, #2C5530, #4A7C59);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        font-weight: bold;
        margin: 0 auto 20px;
    }

    .info-item {
        border-bottom: 1px solid #f8f9fa;
        padding: 15px 0;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .info-value {
        color: #6c757d;
        font-size: 1.1rem;
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .status-approved { background: #d4edda; color: #155724; }
    .status-pending { background: #fff3cd; color: #856404; }
    .status-rejected { background: #f8d7da; color: #721c24; }

    .social-link {
        display: inline-block;
        margin: 5px 10px 5px 0;
        padding: 8px 15px;
        background: #f8f9fa;
        border-radius: 20px;
        text-decoration: none;
        color: #495057;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .social-link:hover {
        background: #e9ecef;
        color: #495057;
        text-decoration: none;
    }

    .gifts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .gift-card {
        background: white;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .gift-card:hover {
        transform: translateY(-3px);
    }

    .gift-image {
        width: 100%;
        height: 150px;
        border-radius: 8px;
        object-fit: cover;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: #6c757d;
        margin-bottom: 10px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">معلومات التاجر</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center text-white mb-3" 
                         style="width: 100px; height: 100px; font-size: 2.5rem;">
                        <?php echo e(substr($vendor->company_name ?? $vendor->user->name, 0, 1)); ?>

                    </div>
                    <h3><?php echo e($vendor->company_name ?? $vendor->user->name); ?></h3>
                    <?php if($vendor->is_approved): ?>
                        <span class="badge bg-success">معتمد</span>
                    <?php else: ?>
                        <span class="badge bg-warning">في الانتظار</span>
                    <?php endif; ?>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <strong>اسم التاجر:</strong> <?php echo e($vendor->user->name); ?><br>
                        <strong>البريد الإلكتروني:</strong> <?php echo e($vendor->user->email); ?><br>
                        <?php if($vendor->user->phone): ?>
                            <strong>الهاتف:</strong> <?php echo e($vendor->user->phone); ?><br>
                        <?php endif; ?>
                        <?php if($vendor->city): ?>
                            <strong>المدينة:</strong> <?php echo e($vendor->city); ?><br>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <?php if($vendor->address): ?>
                            <strong>العنوان:</strong> <?php echo e($vendor->address); ?><br>
                        <?php endif; ?>
                        <strong>تاريخ التسجيل:</strong> <?php echo e($vendor->created_at->format('Y/m/d')); ?><br>
                        <strong>آخر تحديث:</strong> <?php echo e($vendor->updated_at->format('Y/m/d')); ?><br>
                    </div>
                </div>

                <?php if($vendor->description): ?>
                    <div class="mt-3">
                        <strong>وصف النشاط:</strong><br>
                        <p class="text-muted"><?php echo e($vendor->description); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Social Media -->
        <?php if($vendor->website || $vendor->instagram || $vendor->twitter): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">روابط التواصل</h5>
                </div>
                <div class="card-body">
                    <?php if($vendor->website): ?>
                        <a href="<?php echo e($vendor->website); ?>" target="_blank" class="btn btn-outline-primary me-2 mb-2">
                            <i class="fas fa-globe"></i> الموقع الإلكتروني
                        </a>
                    <?php endif; ?>
                    <?php if($vendor->instagram): ?>
                        <a href="<?php echo e($vendor->instagram); ?>" target="_blank" class="btn btn-outline-primary me-2 mb-2">
                            <i class="fab fa-instagram"></i> إنستغرام
                        </a>
                    <?php endif; ?>
                    <?php if($vendor->twitter): ?>
                        <a href="<?php echo e($vendor->twitter); ?>" target="_blank" class="btn btn-outline-primary me-2 mb-2">
                            <i class="fab fa-twitter"></i> تويتر
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <div class="col-lg-4">
        <!-- Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">الإجراءات</h5>
            </div>
            <div class="card-body d-grid gap-2">
                <a href="<?php echo e(route('admin.vendors.edit', $vendor)); ?>" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                
                <?php if(!$vendor->is_approved): ?>
                    <button type="button" class="btn btn-success" onclick="approveVendor(<?php echo e($vendor->id); ?>)">
                        <i class="fas fa-check"></i> اعتماد
                    </button>
                <?php else: ?>
                    <button type="button" class="btn btn-warning" onclick="rejectVendor(<?php echo e($vendor->id); ?>)">
                        <i class="fas fa-times"></i> رفض الاعتماد
                    </button>
                <?php endif; ?>
                
                <a href="<?php echo e(route('admin.vendors.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">الإحصائيات</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي الهدايا:</span>
                    <strong><?php echo e($vendor->gifts->count()); ?></strong>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الهدايا المعتمدة:</span>
                    <strong><?php echo e($vendor->gifts->where('approved', true)->count()); ?></strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>في الانتظار:</span>
                    <strong><?php echo e($vendor->gifts->where('approved', false)->count()); ?></strong>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Gifts -->
<?php if($vendor->gifts->count() > 0): ?>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">هدايا التاجر</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الهدية</th>
                            <th>السعر</th>
                            <th>الفئة</th>
                            <th>الحالة</th>
                            <th>الاعتماد</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $vendor->gifts()->latest()->take(10)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gift): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center text-white" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-gift"></i>
                                        </div>
                                        <strong><?php echo e($gift->name); ?></strong>
                                    </div>
                                </td>
                                <td><?php echo e(number_format($gift->price, 2)); ?> ريال</td>
                                <td><?php echo e($gift->category); ?></td>
                                <td>
                                    <?php switch($gift->status):
                                        case ('available'): ?>
                                            <span class="badge bg-success">متاح</span>
                                            <?php break; ?>
                                        <?php case ('out_of_stock'): ?>
                                            <span class="badge bg-warning">نفد المخزون</span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="badge bg-secondary"><?php echo e($gift->status); ?></span>
                                    <?php endswitch; ?>
                                </td>
                                <td>
                                    <?php if($gift->approved): ?>
                                        <span class="badge bg-success">معتمد</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">في الانتظار</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo e(route('admin.gifts.show', $gift)); ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            
            <?php if($vendor->gifts->count() > 10): ?>
                <div class="text-center mt-3">
                    <a href="<?php echo e(route('admin.gifts.index', ['vendor_id' => $vendor->id])); ?>" class="btn btn-outline-primary">
                        عرض جميع الهدايا (<?php echo e($vendor->gifts->count()); ?>)
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function approveVendor(vendorId) {
    if (confirm('هل أنت متأكد من اعتماد هذا التاجر؟')) {
        fetch(`/admin/vendors/${vendorId}/approve`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في العملية');
            }
        });
    }
}

function rejectVendor(vendorId) {
    if (confirm('هل أنت متأكد من رفض اعتماد هذا التاجر؟')) {
        fetch(`/admin/vendors/${vendorId}/reject`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في العملية');
            }
        });
    }
}
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\hany\GiftsSaudi-Laravel (1)\GiftsSaudi-Laravel (1)\resources\views/admin/vendors/show.blade.php ENDPATH**/ ?>