<div class="form-section">
    <h3 class="section-title text-white">
        <i class="fas fa-filter stage-icon"></i>
        تصفية العملاء
    </h3>
    
    <p class="text-muted mb-4">
        اختر المعايير المناسبة لتحديد العملاء المستهدفين في هذه الحملة
    </p>

    <div class="filter-grid">
        <div>
            <label for="country" class="form-label text-white">الدولة</label>
            <select name="country" id="country" class="form-select">
                <option value="">جميع الدول</option>
                <option value="SA" {{ $campaign->country == 'SA' ? 'selected' : '' }}>السعودية</option>
                <option value="AE" {{ $campaign->country == 'AE' ? 'selected' : '' }}>الإمارات</option>
                <option value="KW" {{ $campaign->country == 'KW' ? 'selected' : '' }}>الكويت</option>
                <option value="QA" {{ $campaign->country == 'QA' ? 'selected' : '' }}>قطر</option>
                <option value="BH" {{ $campaign->country == 'BH' ? 'selected' : '' }}>البحرين</option>
                <option value="OM" {{ $campaign->country == 'OM' ? 'selected' : '' }}>عمان</option>
            </select>
        </div>

        <div>
            <label for="city" class="form-label text-white">المدينة</label>
            <select name="city" id="city" class="form-select">
                <option value="">جميع المدن</option>
                <option value="الرياض" {{ $campaign->city == 'الرياض' ? 'selected' : '' }}>الرياض</option>
                <option value="جدة" {{ $campaign->city == 'جدة' ? 'selected' : '' }}>جدة</option>
                <option value="الدمام" {{ $campaign->city == 'الدمام' ? 'selected' : '' }}>الدمام</option>
                <option value="مكة المكرمة" {{ $campaign->city == 'مكة المكرمة' ? 'selected' : '' }}>مكة المكرمة</option>
                <option value="المدينة المنورة" {{ $campaign->city == 'المدينة المنورة' ? 'selected' : '' }}>المدينة المنورة</option>
                <option value="الطائف" {{ $campaign->city == 'الطائف' ? 'selected' : '' }}>الطائف</option>
                <option value="أبها" {{ $campaign->city == 'أبها' ? 'selected' : '' }}>أبها</option>
                <option value="حائل" {{ $campaign->city == 'حائل' ? 'selected' : '' }}>حائل</option>
                <option value="القصيم" {{ $campaign->city == 'القصيم' ? 'selected' : '' }}>القصيم</option>
                <option value="جازان" {{ $campaign->city == 'جازان' ? 'selected' : '' }}>جازان</option>
            </select>
        </div>

        <div>
            <label for="neighborhood" class="form-label text-white">الحي</label>
            <input type="text" name="neighborhood" id="neighborhood" 
                   class="form-control" 
                   value="{{ $campaign->neighborhood }}"
                   placeholder="أدخل اسم الحي (اختياري)">
        </div>

        <div>
            <label for="gender" class="form-label text-white">الجنس</label>
            <select name="gender" id="gender" class="form-select">
                <option value="">جميع الأجناس</option>
                <option value="ذكر" {{ $campaign->gender == 'ذكر' ? 'selected' : '' }}>ذكر</option>
                <option value="أنثى" {{ $campaign->gender == 'أنثى' ? 'selected' : '' }}>أنثى</option>
            </select>
        </div>

        <div>
            <label for="age_from" class="form-label text-white">العمر من</label>
            <input type="number" name="age_from" id="age_from" 
                   class="form-control" 
                   value="{{ $campaign->age_from }}"
                   min="1" max="100" 
                   placeholder="مثال: 18">
        </div>

        <div>
            <label for="age_to" class="form-label text-white">العمر إلى</label>
            <input type="number" name="age_to" id="age_to" 
                   class="form-control" 
                   value="{{ $campaign->age_to }}"
                   min="1" max="100" 
                   placeholder="مثال: 65">
        </div>

        <div>
            <label for="health_condition" class="form-label text-white">الحالة الصحية</label>
            <select name="health_condition" id="health_condition" class="form-select">
                <option value="">جميع الحالات</option>
                <option value="سليم" {{ $campaign->health_condition == 'سليم' ? 'selected' : '' }}>سليم</option>
                <option value="مرض مزمن" {{ $campaign->health_condition == 'مرض مزمن' ? 'selected' : '' }}>مرض مزمن</option>
                <option value="إعاقة" {{ $campaign->health_condition == 'إعاقة' ? 'selected' : '' }}>إعاقة</option>
                <option value="كبار السن" {{ $campaign->health_condition == 'كبار السن' ? 'selected' : '' }}>كبار السن</option>
            </select>
        </div>

        <div>
            <label for="marital_status" class="form-label text-white">الحالة الاجتماعية</label>
            <select name="marital_status" id="marital_status" class="form-select">
                <option value="">جميع الحالات</option>
                <option value="أعزب" {{ $campaign->marital_status == 'أعزب' ? 'selected' : '' }}>أعزب</option>
                <option value="متزوج" {{ $campaign->marital_status == 'متزوج' ? 'selected' : '' }}>متزوج</option>
                <option value="مطلق" {{ $campaign->marital_status == 'مطلق' ? 'selected' : '' }}>مطلق</option>
                <option value="أرمل" {{ $campaign->marital_status == 'أرمل' ? 'selected' : '' }}>أرمل</option>
            </select>
        </div>
    </div>
</div>

<!-- Client Count Display -->
<div class="client-count-display">
    <h4 class="text-white mb-2">عدد العملاء المستهدفين</h4>
    <div class="client-count-number text-orange" id="clientCount">
        {{ $filteredClientsCount ?? 0 }}
    </div>
    <p class="text-muted mb-0" id="clientCountMessage">
        {{ $filteredClientsCount > 0 ? 'عميل سيتم إرسال الرسائل إليهم' : 'لا توجد عملاء مطابقة للمعايير المختارة' }}
    </p>
</div>

<!-- Filter Summary -->
<div class="form-section">
    <h3 class="section-title text-white">
        <i class="fas fa-list stage-icon"></i>
        ملخص المعايير المختارة
    </h3>
    
    <div class="row" id="filterSummary">
        <!-- This will be populated by JavaScript -->
    </div>
</div>

<script>
let updateTimeout = null;

function updateFilterSummary() {
    const summary = document.getElementById('filterSummary');
    let summaryHtml = '';
    
    const filters = [
        { name: 'country', label: 'الدولة', value: document.querySelector('[name="country"]')?.value },
        { name: 'city', label: 'المدينة', value: document.querySelector('[name="city"]')?.value },
        { name: 'neighborhood', label: 'الحي', value: document.querySelector('[name="neighborhood"]')?.value },
        { name: 'gender', label: 'الجنس', value: document.querySelector('[name="gender"]')?.value },
        { name: 'age_from', label: 'العمر من', value: document.querySelector('[name="age_from"]')?.value },
        { name: 'age_to', label: 'العمر إلى', value: document.querySelector('[name="age_to"]')?.value },
        { name: 'health_condition', label: 'الحالة الصحية', value: document.querySelector('[name="health_condition"]')?.value },
        { name: 'marital_status', label: 'الحالة الاجتماعية', value: document.querySelector('[name="marital_status"]')?.value },
    ];
    
    const activeFilters = filters.filter(f => f.value && f.value.trim() !== '');
    
    if (activeFilters.length === 0) {
        summaryHtml = '<div class="col-12"><p class="text-muted">لم يتم تحديد أي معايير - سيتم استهداف جميع العملاء</p></div>';
    } else {
        activeFilters.forEach(filter => {
            summaryHtml += `
                <div class="col-md-4 mb-2">
                    <div class="badge bg-gradient text-white p-2 w-100">
                        <strong>${filter.label}:</strong> ${filter.value}
                    </div>
                </div>
            `;
        });
    }
    
    summary.innerHTML = summaryHtml;
}

function updateClientCount() {
    // Clear any existing timeout
    if (updateTimeout) {
        clearTimeout(updateTimeout);
    }
    
    // Set a new timeout to debounce the API call
    updateTimeout = setTimeout(() => {
        const filters = {
            country: document.querySelector('[name="country"]')?.value || '',
            city: document.querySelector('[name="city"]')?.value || '',
            neighborhood: document.querySelector('[name="neighborhood"]')?.value || '',
            gender: document.querySelector('[name="gender"]')?.value || '',
            age_from: document.querySelector('[name="age_from"]')?.value || '',
            age_to: document.querySelector('[name="age_to"]')?.value || '',
            health_condition: document.querySelector('[name="health_condition"]')?.value || '',
            marital_status: document.querySelector('[name="marital_status"]')?.value || '',
        };

        // Remove empty filters
        const cleanFilters = {};
        Object.keys(filters).forEach(key => {
            if (filters[key] && filters[key].trim() !== '') {
                cleanFilters[key] = filters[key];
            }
        });

        console.log('Updating client count with filters:', cleanFilters);

        // Show loading state
        const countDisplay = document.getElementById('clientCount');
        const countMessage = document.getElementById('clientCountMessage');
        
        if (countDisplay) {
            countDisplay.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        }

        fetch('/admin/campaigns/filtered-clients?' + new URLSearchParams(cleanFilters), {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Client count response:', data);
            if (countDisplay) {
                countDisplay.textContent = data.count.toLocaleString('ar-SA');
            }
            if (countMessage) {
                countMessage.textContent = data.count > 0 
                    ? `عميل سيتم إرسال الرسائل إليهم` 
                    : 'لا توجد عملاء مطابقة للمعايير المختارة';
            }
        })
        .catch(error => {
            console.error('Error updating client count:', error);
            if (countDisplay) {
                countDisplay.textContent = '0';
            }
            if (countMessage) {
                countMessage.textContent = 'حدث خطأ في تحديث العدد';
            }
        });
    }, 300); // 300ms delay for debouncing
}

// Initialize summary and set up event listeners
document.addEventListener('DOMContentLoaded', function() {
    updateFilterSummary();
    updateClientCount(); // Initial count
    
    // Update summary and count when any filter changes
    document.querySelectorAll('.filter-grid input, .filter-grid select').forEach(input => {
        input.addEventListener('change', function() {
            updateFilterSummary();
            updateClientCount();
        });
        
        // For input fields, also listen to 'input' event for real-time updates
        if (input.type === 'text' || input.type === 'number') {
            input.addEventListener('input', function() {
                updateFilterSummary();
                updateClientCount();
            });
        }
    });
    
    console.log('Client filtering initialized');
});
</script> 