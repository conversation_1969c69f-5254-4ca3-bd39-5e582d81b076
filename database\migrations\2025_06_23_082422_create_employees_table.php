<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
            
            // Basic Information
            $table->string('name');
            $table->string('email')->unique();
            $table->string('phone')->unique();
            $table->string('national_id')->unique();
            $table->enum('gender', ['male', 'female']);
            $table->date('birth_date');
            
            // Employment Information
            $table->foreignId('vendor_id')->constrained('vendors')->onDelete('cascade');
            $table->string('employee_id')->unique(); // Custom employee ID
            $table->string('position');
            $table->enum('employment_type', ['full_time', 'part_time', 'contract']);
            $table->date('hire_date');
            $table->decimal('salary', 10, 2)->nullable();
            
            // Contact Information
            $table->text('address');
            $table->string('emergency_contact_name');
            $table->string('emergency_contact_phone');
            
            // Status and Performance
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending'])->default('active');
            $table->integer('total_deliveries')->default(0);
            $table->decimal('performance_rating', 3, 2)->default(0.00); // 0.00 to 5.00
            $table->text('notes')->nullable();
            
            // Authentication
            $table->string('password')->nullable(); // For employee login if needed
            $table->timestamp('last_login_at')->nullable();
            
            // File uploads
            $table->string('profile_photo')->nullable();
            $table->string('id_document')->nullable();
            $table->string('contract_document')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['vendor_id', 'status']);
            $table->index('employee_id');
            $table->index('hire_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
