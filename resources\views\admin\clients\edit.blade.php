@extends('layouts.admin')

@section('title', 'تعديل العميل')
@section('page-title', 'تعديل العميل')

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">تعديل بيانات العميل</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.clients.update', $client) }}" method="POST">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ old('name', $client->name) }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ old('email', $client->email) }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="phone" name="phone" 
                                   value="{{ old('phone', $client->phone) }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">المدينة</label>
                            <input type="text" class="form-control" id="city" name="city" 
                                   value="{{ old('city', $client->city) }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" {{ old('status', $client->status) == 'active' ? 'selected' : '' }}>نشط</option>
                                <option value="inactive" {{ old('status', $client->status) == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                <option value="pending" {{ old('status', $client->status) == 'pending' ? 'selected' : '' }}>في الانتظار</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="payment_status" class="form-label">حالة الدفع</label>
                            <select class="form-select" id="payment_status" name="payment_status">
                                <option value="unpaid" {{ old('payment_status', $client->payment_status) == 'unpaid' ? 'selected' : '' }}>غير مدفوع</option>
                                <option value="paid" {{ old('payment_status', $client->payment_status) == 'paid' ? 'selected' : '' }}>مدفوع</option>
                                <option value="partial" {{ old('payment_status', $client->payment_status) == 'partial' ? 'selected' : '' }}>مدفوع جزئياً</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="total_amount" class="form-label">المبلغ الإجمالي</label>
                            <input type="number" class="form-control" id="total_amount" name="total_amount" 
                                   step="0.01" value="{{ old('total_amount', $client->total_amount) }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="paid_amount" class="form-label">المبلغ المدفوع</label>
                            <input type="number" class="form-control" id="paid_amount" name="paid_amount" 
                                   step="0.01" value="{{ old('paid_amount', $client->paid_amount) }}">
                        </div>
                    </div>
                    
                    @if(!empty($dynamicFields))
                        <!-- Dynamic Fields Section -->
                        <h6 class="mb-3 text-primary">
                            <i class="fas fa-plus-square me-2"></i>
                            الحقول الديناميكية (من استيراد إكسل)
                        </h6>
                        
                        <div class="row mb-4">
                            @foreach($dynamicFields as $field)
                                <div class="col-md-6 mb-3">
                                    <label for="dynamic_{{ $field }}" class="form-label">{{ $field }}</label>
                                    <input type="text" class="form-control" 
                                           id="dynamic_{{ $field }}" 
                                           name="custom_{{ $field }}" 
                                           value="{{ old('custom_' . $field, $client->getDynamicField($field)) }}"
                                           placeholder="أدخل {{ $field }}">
                                </div>
                            @endforeach
                        </div>
                    @endif
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                        <a href="{{ route('admin.clients.show', $client) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">معلومات إضافية</h5>
            </div>
            <div class="card-body">
                <p><strong>رقم العميل:</strong> #{{ $client->id }}</p>
                <p><strong>تاريخ التسجيل:</strong> {{ $client->created_at ? $client->created_at->format('Y/m/d') : 'غير محدد' }}</p>
                <p><strong>آخر تحديث:</strong> {{ $client->updated_at ? $client->updated_at->format('Y/m/d') : 'غير محدد' }}</p>
            </div>
        </div>
    </div>
</div>
@endsection 