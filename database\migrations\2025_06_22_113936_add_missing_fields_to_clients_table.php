<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            // Add marital status field
            $table->enum('marital_status', ['أعزب', 'متزوج', 'مطلق', 'أرمل'])->nullable()->after('health_condition');
            
            // Add status field  
            $table->enum('status', ['active', 'inactive', 'pending'])->default('active')->after('additional_fields');
            
            // Add index for better performance
            $table->index('marital_status');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clients', function (Blueprint $table) {
            $table->dropIndex(['marital_status']);
            $table->dropIndex(['status']);
            $table->dropColumn(['marital_status', 'status']);
        });
    }
};
