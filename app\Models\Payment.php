<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'amount',
        'order_reference',
        'customer_name',
        'customer_email',
        'customer_phone',
        'payment_id',
        'transaction_id',
        'payment_method',
        'payment_status',
        'metadata',
        'test_mode'
    ];

    protected $casts = [
        'metadata' => 'array',
        'test_mode' => 'boolean',
        'amount' => 'decimal:2'
    ];

    // Payment status constants
    const STATUS_INITIATED = 'INITIATED';
    const STATUS_PENDING = 'PENDING';
    const STATUS_PAID = 'PAID';
    const STATUS_FAILED = 'FAILED';
    const STATUS_EXPIRED = 'EXPIRED';
    const STATUS_CANCELLED = 'CANCELLED';

    // Scopes
    public function scopePaid($query)
    {
        return $query->where('payment_status', self::STATUS_PAID);
    }

    public function scopePending($query)
    {
        return $query->where('payment_status', self::STATUS_PENDING);
    }

    public function scopeFailed($query)
    {
        return $query->where('payment_status', self::STATUS_FAILED);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    // Helper methods
    public function isPaid()
    {
        return $this->payment_status === self::STATUS_PAID;
    }

    public function isPending()
    {
        return $this->payment_status === self::STATUS_PENDING;
    }

    public function isFailed()
    {
        return $this->payment_status === self::STATUS_FAILED;
    }

    public function getStatusBadgeClassAttribute()
    {
        $classes = [
            self::STATUS_PAID => 'badge-success',
            self::STATUS_PENDING => 'badge-warning',
            self::STATUS_FAILED => 'badge-danger',
            self::STATUS_EXPIRED => 'badge-secondary',
            self::STATUS_CANCELLED => 'badge-dark',
            self::STATUS_INITIATED => 'badge-info'
        ];

        return $classes[$this->payment_status] ?? 'badge-secondary';
    }

    public function getStatusDisplayNameAttribute()
    {
        $names = [
            self::STATUS_PAID => 'مدفوعة',
            self::STATUS_PENDING => 'في الانتظار',
            self::STATUS_FAILED => 'فاشلة',
            self::STATUS_EXPIRED => 'منتهية الصلاحية',
            self::STATUS_CANCELLED => 'ملغية',
            self::STATUS_INITIATED => 'مبدأة'
        ];

        return $names[$this->payment_status] ?? 'غير محدد';
    }

    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 2) . ' ريال';
    }

    // Relationships (if needed later)
    public function campaign()
    {
        return $this->belongsTo(GiftCampaign::class, 'order_reference', 'id');
    }
}
