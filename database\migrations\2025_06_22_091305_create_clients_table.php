<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            
            // Required fields
            $table->string('phone')->unique();
            $table->string('country');
            $table->string('city');
            $table->string('neighborhood');
            $table->enum('gender', ['Male', 'Female']);
            $table->integer('age');
            $table->enum('health_condition', ['Healthy', 'Chronic']);
            
            // Optional fields
            $table->string('email')->nullable();
            $table->string('name')->nullable();
            $table->date('birthdate')->nullable();
            $table->string('occupation')->nullable();
            
            // Campaign and Gift relations
            $table->foreignId('campaign_id')->nullable()->constrained('gift_campaigns')->onDelete('set null');
            $table->foreignId('gift_id')->nullable()->constrained()->onDelete('set null');
            
            // Message tracking
            $table->boolean('message_received')->default(false);
            $table->timestamp('message_sent_at')->nullable();
            
            // Gift tracking
            $table->boolean('gift_received')->default(false);
            $table->timestamp('gift_received_at')->nullable();
            
            // Feedback
            $table->integer('rating')->nullable();
            $table->text('feedback')->nullable();
            
            // Payment information
            $table->decimal('amount_paid', 10, 2)->default(0);
            $table->string('payment_method')->nullable();
            $table->enum('payment_status', ['paid', 'unpaid'])->default('unpaid');
            
            // Staff assignment
            $table->string('assigned_employee')->nullable();
            
            // Dynamic fields storage (JSON)
            $table->json('additional_fields')->nullable();
            
            $table->timestamps();
            
            // Indexes for better performance
            $table->index('phone');
            $table->index('country');
            $table->index('city');
            $table->index('gender');
            $table->index('health_condition');
            $table->index(['campaign_id', 'gift_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
