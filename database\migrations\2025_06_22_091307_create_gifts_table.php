<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gifts', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->enum('status', ['تم التسليم', 'قيد الانتظار', 'تم الرفض'])->default('قيد الانتظار');
            $table->decimal('price', 10, 2);
            $table->string('image')->nullable();
            $table->boolean('approved')->default(false);
            $table->foreignId('vendor_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            
            $table->index('status');
            $table->index('approved');
            $table->index('vendor_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gifts');
    }
};
