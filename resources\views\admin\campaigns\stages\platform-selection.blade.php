<div class="form-section">
    <h3 class="section-title text-white">
        <i class="fas fa-share-alt stage-icon"></i>
        اختيار منصة الإرسال
    </h3>
    
    <p class="text-muted mb-4">
        اختر المنصة التي تريد استخدامها لإرسال رسائل الحملة
    </p>

    <div class="row">
        <div class="col-md-6">
            <div class="platform-option {{ $campaign->platform == 'own' ? 'selected' : '' }}" 
                 onclick="selectPlatform('own')">
                <div class="platform-card p-4 rounded h-100">
                    <div class="text-center mb-3">
                        <i class="fab fa-whatsapp fa-4x text-success"></i>
                    </div>
                    <h4 class="text-center text-white mb-3">واتساب الخاص</h4>
                    <div class="platform-features">
                        <div class="feature-item mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <span class="text-muted">إرسال فوري</span>
                        </div>
                        <div class="feature-item mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <span class="text-muted">تحكم كامل</span>
                        </div>
                        <div class="feature-item mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <span class="text-muted">تتبع مباشر</span>
                        </div>
                        <div class="feature-item mb-2">
                            <i class="fas fa-times text-danger me-2"></i>
                            <span class="text-muted">يتطلب دفع مسبق</span>
                        </div>
                    </div>
                    <div class="platform-cost mt-3 p-3 rounded" style="background: rgba(0, 0, 0, 0.3);">
                        <div class="text-center">
                            <h5 class="text-orange mb-1">0.25 ريال/رسالة</h5>
                            <small class="text-muted">+ ضريبة القيمة المضافة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="platform-option {{ $campaign->platform == 'external' ? 'selected' : '' }}" 
                 onclick="selectPlatform('external')">
                <div class="platform-card p-4 rounded h-100">
                    <div class="text-center mb-3">
                        <i class="fas fa-cloud fa-4x text-primary"></i>
                    </div>
                    <h4 class="text-center text-white mb-3">منصة خارجية</h4>
                    <div class="platform-features">
                        <div class="feature-item mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <span class="text-muted">بدون دفع مسبق</span>
                        </div>
                        <div class="feature-item mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <span class="text-muted">حدود أعلى للإرسال</span>
                        </div>
                        <div class="feature-item mb-2">
                            <i class="fas fa-times text-danger me-2"></i>
                            <span class="text-muted">يتطلب موافقة الإدارة</span>
                        </div>
                        <div class="feature-item mb-2">
                            <i class="fas fa-times text-danger me-2"></i>
                            <span class="text-muted">قد يستغرق وقت أطول</span>
                        </div>
                    </div>
                    <div class="platform-cost mt-3 p-3 rounded" style="background: rgba(0, 0, 0, 0.3);">
                        <div class="text-center">
                            <h5 class="text-orange mb-1">حسب العقد</h5>
                            <small class="text-muted">دفع شهري</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" name="platform" id="platform" value="{{ $campaign->platform ?? 'own' }}">
</div>

<!-- Messaging Channel Selection -->
<div class="form-section mt-4">
    <h3 class="section-title text-white">
        <i class="fas fa-comments stage-icon"></i>
        قناة الإرسال
    </h3>

    <p class="text-muted mb-4">
        اختر قناة الإرسال المناسبة لحملتك
    </p>

    <div class="row">
        <div class="col-md-4">
            <div class="channel-option {{ ($campaign->messaging_channel ?? 'whatsapp') == 'whatsapp' ? 'selected' : '' }}"
                 onclick="selectChannel('whatsapp')">
                <div class="channel-card p-4 rounded h-100">
                    <div class="text-center mb-3">
                        <i class="fab fa-whatsapp fa-3x text-success"></i>
                    </div>
                    <h5 class="text-center text-white mb-2">WhatsApp</h5>
                    <p class="text-center text-muted mb-3">رسائل نصية وصور</p>
                    <div class="text-center">
                        <small class="text-success">معدل الوصول: 98%</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="channel-option {{ $campaign->messaging_channel == 'email' ? 'selected' : '' }}"
                 onclick="selectChannel('email')">
                <div class="channel-card p-4 rounded h-100">
                    <div class="text-center mb-3">
                        <i class="fas fa-envelope fa-3x text-primary"></i>
                    </div>
                    <h5 class="text-center text-white mb-2">البريد الإلكتروني</h5>
                    <p class="text-center text-muted mb-3">رسائل غنية بالمحتوى</p>
                    <div class="text-center">
                        <small class="text-info">معدل الوصول: 85%</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="channel-option {{ $campaign->messaging_channel == 'sms' ? 'selected' : '' }}"
                 onclick="selectChannel('sms')">
                <div class="channel-card p-4 rounded h-100">
                    <div class="text-center mb-3">
                        <i class="fas fa-sms fa-3x text-warning"></i>
                    </div>
                    <h5 class="text-center text-white mb-2">الرسائل النصية</h5>
                    <p class="text-center text-muted mb-3">رسائل نصية مباشرة</p>
                    <div class="text-center">
                        <small class="text-warning">معدل الوصول: 95%</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" name="messaging_channel" id="messaging_channel" value="{{ $campaign->messaging_channel ?? 'whatsapp' }}">
</div>

<!-- WhatsApp Token Field (for external platform) -->
<div class="form-section" id="whatsappTokenField" style="display: {{ $campaign->platform == 'external' ? 'block' : 'none' }};">
    <h3 class="section-title text-white">
        <i class="fas fa-key stage-icon"></i>
        إعدادات المنصة الخارجية
    </h3>
    
    <div class="row">
        <div class="col-md-6">
            <label for="whatsapp_token" class="form-label text-white">رمز الواتساب (Token)</label>
            <input type="text" name="whatsapp_token" id="whatsapp_token" 
                   class="form-control" 
                   value="{{ $campaign->whatsapp_token }}"
                   placeholder="أدخل رمز الواتساب الخاص بك">
            <small class="text-muted">
                الرمز المقدم من مقدم الخدمة الخارجي
            </small>
        </div>
        
        <div class="col-md-6">
            <label for="external_provider" class="form-label text-white">مقدم الخدمة</label>
            <select name="external_provider" id="external_provider" class="form-select">
                <option value="">اختر مقدم الخدمة...</option>
                <option value="twilio" {{ $campaign->external_provider == 'twilio' ? 'selected' : '' }}>Twilio</option>
                <option value="whatsapp_business" {{ $campaign->external_provider == 'whatsapp_business' ? 'selected' : '' }}>WhatsApp Business API</option>
                <option value="clickatell" {{ $campaign->external_provider == 'clickatell' ? 'selected' : '' }}>Clickatell</option>
                <option value="other" {{ $campaign->external_provider == 'other' ? 'selected' : '' }}>آخر</option>
            </select>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-md-12">
            <label for="external_notes" class="form-label text-white">ملاحظات إضافية</label>
            <textarea name="external_notes" id="external_notes" 
                      class="form-control" 
                      rows="3" 
                      placeholder="أي معلومات إضافية حول الحساب أو الإعدادات...">{{ $campaign->external_notes }}</textarea>
        </div>
    </div>

    <!-- External Platform Warning -->
    <div class="alert alert-warning mt-3" style="background: rgba(255, 193, 7, 0.1); border: 1px solid #ffc107; color: #ffc107;">
        <div class="d-flex align-items-start">
            <i class="fas fa-exclamation-triangle me-3 mt-1"></i>
            <div>
                <h6 class="alert-heading">تنبيه هام</h6>
                <p class="mb-2">استخدام المنصة الخارجية يتطلب:</p>
                <ul class="mb-0">
                    <li>موافقة الإدارة على الحملة</li>
                    <li>التحقق من صحة رمز الواتساب</li>
                    <li>قد يستغرق الأمر 24-48 ساعة للمراجعة</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Payment Information (for own platform) -->
<div class="form-section" id="paymentInfoField" style="display: {{ $campaign->platform == 'own' ? 'block' : 'none' }};">
    <h3 class="section-title text-white">
        <i class="fas fa-credit-card stage-icon"></i>
        معلومات الدفع
    </h3>
    
    <div class="payment-summary p-4 rounded" style="background: rgba(42, 42, 42, 0.6); border: 1px solid #555;">
        <div class="row">
            <div class="col-md-8">
                <h5 class="text-white mb-3">ملخص الفاتورة</h5>
                <div class="invoice-item d-flex justify-content-between mb-2">
                    <span class="text-muted">عدد الرسائل:</span>
                    <span class="text-white" id="invoiceMessageCount">{{ $campaign->message_count ?? 0 }}</span>
                </div>
                <div class="invoice-item d-flex justify-content-between mb-2">
                    <span class="text-muted">سعر الرسالة الواحدة:</span>
                    <span class="text-white">0.25 ريال</span>
                </div>
                <div class="invoice-item d-flex justify-content-between mb-2">
                    <span class="text-muted">المجموع الفرعي:</span>
                    <span class="text-white" id="invoiceSubtotal">{{ number_format(($campaign->message_count ?? 0) * 0.25, 2) }} ريال</span>
                </div>
                <div class="invoice-item d-flex justify-content-between mb-2">
                    <span class="text-muted">ضريبة القيمة المضافة (15%):</span>
                    <span class="text-white" id="invoiceVat">{{ number_format(($campaign->message_count ?? 0) * 0.25 * 0.15, 2) }} ريال</span>
                </div>
                <hr style="border-color: #555;">
                <div class="invoice-item d-flex justify-content-between">
                    <span class="text-orange h5">المجموع النهائي:</span>
                    <span class="text-orange h5" id="invoiceTotal">{{ number_format(($campaign->message_count ?? 0) * 0.25 * 1.15, 2) }} ريال</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="payment-methods">
                    <h6 class="text-white mb-3">وسائل الدفع المتاحة</h6>
                    <div class="payment-method mb-2">
                        <i class="fas fa-credit-card text-orange me-2"></i>
                        <span class="text-muted">بطاقة ائتمانية</span>
                    </div>
                    <div class="payment-method mb-2">
                        <i class="fas fa-university text-orange me-2"></i>
                        <span class="text-muted">حوالة بنكية</span>
                    </div>
                    <div class="payment-method mb-2">
                        <i class="fas fa-mobile-alt text-orange me-2"></i>
                        <span class="text-muted">محفظة رقمية</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.platform-option {
    cursor: pointer;
    transition: all 0.3s ease;
}

.platform-card {
    background: linear-gradient(135deg, #2a2a2a 0%, #3d3d3d 100%);
    border: 2px solid transparent;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
}

.platform-option:hover .platform-card {
    border-color: #ff6b35;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
}

.platform-option.selected .platform-card {
    border-color: #f7931e;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
    box-shadow: 0 15px 40px rgba(255, 107, 53, 0.3);
}

.feature-item {
    display: flex;
    align-items: center;
}

.payment-summary {
    background: linear-gradient(135deg, rgba(42, 42, 42, 0.8), rgba(61, 61, 61, 0.8));
}

.invoice-item {
    padding: 5px 0;
}

.channel-option {
    cursor: pointer;
    transition: all 0.3s ease;
}

.channel-card {
    background: linear-gradient(135deg, #2a2a2a 0%, #3d3d3d 100%);
    border: 2px solid transparent;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
}

.channel-option:hover .channel-card {
    border-color: #ff6b35;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
}

.channel-option.selected .channel-card {
    border-color: #f7931e;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
    box-shadow: 0 15px 40px rgba(255, 107, 53, 0.3);
}
</style>

<script>
function selectPlatform(platform) {
    // Update visual selection
    document.querySelectorAll('.platform-option').forEach(option => {
        option.classList.remove('selected');
    });
    event.currentTarget.classList.add('selected');

    // Update hidden input
    document.getElementById('platform').value = platform;

    // Show/hide relevant fields
    const whatsappTokenField = document.getElementById('whatsappTokenField');
    const paymentInfoField = document.getElementById('paymentInfoField');

    if (platform === 'external') {
        whatsappTokenField.style.display = 'block';
        paymentInfoField.style.display = 'none';
    } else {
        whatsappTokenField.style.display = 'none';
        paymentInfoField.style.display = 'block';
    }
}

function selectChannel(channel) {
    // Update visual selection
    document.querySelectorAll('.channel-option').forEach(option => {
        option.classList.remove('selected');
    });
    event.currentTarget.classList.add('selected');

    // Update hidden input
    document.getElementById('messaging_channel').value = channel;
}

// Initialize platform and channel selection
document.addEventListener('DOMContentLoaded', function() {
    const selectedPlatform = document.getElementById('platform').value;
    if (selectedPlatform) {
        const platformElement = document.querySelector(`[onclick="selectPlatform('${selectedPlatform}')"]`);
        if (platformElement) {
            platformElement.classList.add('selected');
        }
    }

    const selectedChannel = document.getElementById('messaging_channel').value;
    if (selectedChannel) {
        const channelElement = document.querySelector(`[onclick="selectChannel('${selectedChannel}')"]`);
        if (channelElement) {
            channelElement.classList.add('selected');
        }
    }
});
</script> 