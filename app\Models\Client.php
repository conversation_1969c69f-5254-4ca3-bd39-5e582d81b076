<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Client extends Model
{
    use HasFactory;

    protected $fillable = [
        'phone',
        'country',
        'city',
        'neighborhood',
        'gender',
        'age',
        'health_condition',
        'email',
        'name',
        'birthdate',
        'occupation',
        'campaign_id',
        'gift_id',
        'message_received',
        'message_sent_at',
        'gift_received',
        'gift_received_at',
        'rating',
        'feedback',
        'amount_paid',
        'total_amount',
        'paid_amount',
        'remaining_amount',
        'payment_date',
        'payment_method',
        'payment_status',
        'assigned_employee',
        'additional_fields',
        'status',
    ];

    protected $casts = [
        'birthdate' => 'date',
        'message_sent_at' => 'datetime',
        'gift_received_at' => 'datetime',
        'payment_date' => 'datetime',
        'message_received' => 'boolean',
        'gift_received' => 'boolean',
        'amount_paid' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'additional_fields' => 'array',
        'rating' => 'integer',
        'age' => 'integer',
    ];

    /**
     * Get the campaign associated with the client
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(GiftCampaign::class);
    }

    /**
     * Get the gift associated with the client
     */
    public function gift(): BelongsTo
    {
        return $this->belongsTo(Gift::class);
    }

    /**
     * Check if client has received message
     */
    public function hasReceivedMessage(): bool
    {
        return $this->message_received;
    }

    /**
     * Check if client has received gift
     */
    public function hasReceivedGift(): bool
    {
        return $this->gift_received;
    }

    /**
     * Check if payment is completed
     */
    public function isPaid(): bool
    {
        return $this->payment_status === 'paid';
    }

    /**
     * Get full address
     */
    public function getFullAddressAttribute(): string
    {
        return trim("{$this->neighborhood}, {$this->city}, {$this->country}");
    }

    /**
     * Scope for filtering by gender
     */
    public function scopeGender($query, $gender)
    {
        return $query->where('gender', $gender);
    }

    /**
     * Scope for filtering by age range
     */
    public function scopeAgeRange($query, $from, $to)
    {
        return $query->whereBetween('age', [$from, $to]);
    }

    /**
     * Scope for filtering by health condition
     */
    public function scopeHealthCondition($query, $condition)
    {
        return $query->where('health_condition', $condition);
    }

    /**
     * Scope for clients who received messages
     */
    public function scopeMessageReceived($query)
    {
        return $query->where('message_received', true);
    }

    /**
     * Scope for clients who received gifts
     */
    public function scopeGiftReceived($query)
    {
        return $query->where('gift_received', true);
    }

    /**
     * Scope for paid clients
     */
    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    /**
     * Get clients with dynamic fields
     */
    public function getAllFieldsAttribute(): array
    {
        $data = $this->toArray();
        
        if ($this->additional_fields) {
            $data = array_merge($data, $this->additional_fields);
        }
        
        return $data;
    }

    /**
     * Store dynamic field
     */
    public function setDynamicField($key, $value): void
    {
        $additionalFields = $this->additional_fields ?? [];
        $additionalFields[$key] = $value;
        $this->additional_fields = $additionalFields;
        $this->save();
    }

    /**
     * Get dynamic field
     */
    public function getDynamicField($key, $default = null)
    {
        $value = $this->additional_fields[$key] ?? $default;
        
        // Handle array values
        if (is_array($value)) {
            return implode(', ', $value);
        }
        
        return $value;
    }

    /**
     * Get formatted dynamic field value for display
     */
    public function getFormattedDynamicField($key, $default = '-')
    {
        if (!$this->additional_fields || !isset($this->additional_fields[$key])) {
            return $default;
        }
        
        $value = $this->additional_fields[$key];
        
        // Handle different data types
        if (is_array($value)) {
            return implode(', ', array_filter($value));
        }
        
        if (is_bool($value)) {
            return $value ? 'نعم' : 'لا';
        }
        
        if (is_null($value) || $value === '') {
            return $default;
        }
        
        return (string) $value;
    }
}
