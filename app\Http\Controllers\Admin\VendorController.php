<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Vendor;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class VendorController extends Controller
{
    /**
     * Display a listing of the vendors
     */
    public function index(Request $request)
    {
        $query = Vendor::with('user')
            ->withCount(['gifts', 'approvedGifts']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('company_name', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('status')) {
            if ($request->status === 'approved') {
                $query->approved();
            } elseif ($request->status === 'pending') {
                $query->pending();
            }
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $vendors = $query->paginate(15)->withQueryString();

        // Get filter options
        $categories = Vendor::whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->sort();

        $stats = [
            'total' => Vendor::count(),
            'approved' => Vendor::approved()->count(),
            'pending' => Vendor::pending()->count(),
        ];

        return view('admin.vendors.index', compact('vendors', 'categories', 'stats'));
    }

    /**
     * Show the form for creating a new vendor
     */
    public function create()
    {
        return view('admin.vendors.create');
    }

    /**
     * Store a newly created vendor
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'username' => 'required|string|max:255|unique:vendors',
            'company_name' => 'required|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'website' => 'nullable|url|max:255',
            'category' => 'nullable|string|max:100',
            'description' => 'nullable|string|max:1000',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            
            // Social Media
            'facebook' => 'nullable|url|max:255',
            'instagram' => 'nullable|url|max:255',
            'twitter' => 'nullable|url|max:255',
            
            // Bank Info
            'bank_name' => 'nullable|string|max:255',
            'account_name' => 'nullable|string|max:255',
            'account_number' => 'nullable|string|max:50',
            'iban' => 'nullable|string|max:50',
        ]);

        DB::beginTransaction();
        
        try {
            // Create User
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => 'vendor',
                'phone' => $request->phone_number,
            ]);

            // Handle logo upload
            $logoUrl = null;
            if ($request->hasFile('logo')) {
                $logoPath = $request->file('logo')->store('vendors/logos', 'public');
                $logoUrl = $logoPath;
            }

            // Create Vendor
            $vendor = Vendor::create([
                'user_id' => $user->id,
                'username' => $request->username,
                'company_name' => $request->company_name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'address' => $request->address,
                'website' => $request->website,
                'category' => $request->category,
                'description' => $request->description,
                'logo_url' => $logoUrl,
                'facebook' => $request->facebook,
                'instagram' => $request->instagram,
                'twitter' => $request->twitter,
                'bank_name' => $request->bank_name,
                'account_name' => $request->account_name,
                'account_number' => $request->account_number,
                'iban' => $request->iban,
                'is_approved' => $request->boolean('auto_approve', false),
            ]);

            DB::commit();

            return redirect()->route('admin.vendors.index')
                ->with('success', 'تم إنشاء التاجر بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء التاجر: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified vendor
     */
    public function show(Vendor $vendor)
    {
        $vendor->load(['user', 'gifts' => function($query) {
            $query->latest()->take(10);
        }]);

        $stats = [
            'total_gifts' => $vendor->gifts()->count(),
            'approved_gifts' => $vendor->approvedGifts()->count(),
            'pending_gifts' => $vendor->gifts()->where('approved', false)->count(),
            'delivered_gifts' => $vendor->gifts()->where('status', 'تم التسليم')->count(),
        ];

        return view('admin.vendors.show', compact('vendor', 'stats'));
    }

    /**
     * Show the form for editing the vendor
     */
    public function edit(Vendor $vendor)
    {
        $vendor->load('user');
        return view('admin.vendors.edit', compact('vendor'));
    }

    /**
     * Update the specified vendor
     */
    public function update(Request $request, Vendor $vendor)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', 
                      Rule::unique('users')->ignore($vendor->user_id)],
            'username' => ['required', 'string', 'max:255', 
                          Rule::unique('vendors')->ignore($vendor->id)],
            'company_name' => 'required|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'website' => 'nullable|url|max:255',
            'category' => 'nullable|string|max:100',
            'description' => 'nullable|string|max:1000',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            
            // Social Media
            'facebook' => 'nullable|url|max:255',
            'instagram' => 'nullable|url|max:255',
            'twitter' => 'nullable|url|max:255',
            
            // Bank Info
            'bank_name' => 'nullable|string|max:255',
            'account_name' => 'nullable|string|max:255',
            'account_number' => 'nullable|string|max:50',
            'iban' => 'nullable|string|max:50',
        ]);

        DB::beginTransaction();
        
        try {
            // Update User
            $vendor->user->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone_number,
            ]);

            // Handle logo upload
            $logoUrl = $vendor->logo_url;
            if ($request->hasFile('logo')) {
                // Delete old logo
                if ($logoUrl) {
                    Storage::disk('public')->delete($logoUrl);
                }
                
                $logoPath = $request->file('logo')->store('vendors/logos', 'public');
                $logoUrl = $logoPath;
            }

            // Update Vendor
            $vendor->update([
                'username' => $request->username,
                'company_name' => $request->company_name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'address' => $request->address,
                'website' => $request->website,
                'category' => $request->category,
                'description' => $request->description,
                'logo_url' => $logoUrl,
                'facebook' => $request->facebook,
                'instagram' => $request->instagram,
                'twitter' => $request->twitter,
                'bank_name' => $request->bank_name,
                'account_name' => $request->account_name,
                'account_number' => $request->account_number,
                'iban' => $request->iban,
            ]);

            DB::commit();

            return redirect()->route('admin.vendors.show', $vendor)
                ->with('success', 'تم تحديث بيانات التاجر بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث البيانات: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified vendor
     */
    public function destroy(Vendor $vendor)
    {
        DB::beginTransaction();
        
        try {
            // Delete logo file
            if ($vendor->logo_url) {
                Storage::disk('public')->delete($vendor->logo_url);
            }

            // Delete vendor and user
            $vendor->user->delete(); // This will cascade delete the vendor
            
            DB::commit();

            return redirect()->route('admin.vendors.index')
                ->with('success', 'تم حذف التاجر بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            
            return back()->with('error', 'حدث خطأ أثناء حذف التاجر: ' . $e->getMessage());
        }
    }

    /**
     * Approve a vendor
     */
    public function approve(Request $request, Vendor $vendor)
    {
        $vendor->update(['is_approved' => true]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'تم اعتماد التاجر بنجاح'
            ]);
        }

        return redirect()->route('admin.vendors.index')
            ->with('success', 'تم اعتماد التاجر بنجاح');
    }

    /**
     * Reject a vendor
     */
    public function reject(Request $request, Vendor $vendor)
    {
        $vendor->update(['is_approved' => false]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'تم رفض التاجر'
            ]);
        }

        return redirect()->route('admin.vendors.index')
            ->with('success', 'تم رفض التاجر');
    }

    /**
     * Get vendor statistics for AJAX
     */
    public function statistics()
    {
        $stats = [
            'total' => Vendor::count(),
            'approved' => Vendor::approved()->count(),
            'pending' => Vendor::pending()->count(),
            'with_gifts' => Vendor::has('gifts')->count(),
            'categories' => Vendor::whereNotNull('category')
                ->groupBy('category')
                ->selectRaw('category, COUNT(*) as count')
                ->get(),
        ];

        return response()->json($stats);
    }
}
