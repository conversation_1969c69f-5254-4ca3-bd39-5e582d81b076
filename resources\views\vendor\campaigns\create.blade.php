@extends('layouts.vendor')

@section('title', 'إنشاء حملة إعلانية جديدة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-bullhorn me-2"></i>
                    إنشاء حملة إعلانية جديدة
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('vendor.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('vendor.campaigns.index') }}">الحملات الإعلانية</a></li>
                        <li class="breadcrumb-item active" aria-current="page">إنشاء حملة جديدة</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card campaign-create-card">
                <div class="card-header bg-gradient-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-rocket me-2"></i>
                        معلومات الحملة الأساسية
                    </h3>
                    <p class="card-subtitle mb-0 mt-2 opacity-75">
                        أدخل التفاصيل الأساسية لحملتك الإعلانية الجديدة
                    </p>
                </div>

                <div class="card-body p-4">
                    <form action="{{ route('vendor.campaigns.store') }}" method="POST" class="campaign-form">
                        @csrf

                        <!-- Campaign Basic Information -->
                        <div class="form-section">
                            <h4 class="section-title">
                                <i class="fas fa-info-circle section-icon"></i>
                                معلومات الحملة
                            </h4>
                            
                            <div class="row g-3">
                                <div class="col-md-12">
                                    <label for="campaign_name" class="form-label required">
                                        <i class="fas fa-tag me-1"></i>
                                        اسم الحملة
                                    </label>
                                    <input type="text" 
                                           id="campaign_name" 
                                           name="campaign_name" 
                                           class="form-control @error('campaign_name') is-invalid @enderror" 
                                           value="{{ old('campaign_name') }}" 
                                           placeholder="مثال: حملة العروض الصيفية لعام 2025"
                                           required>
                                    @error('campaign_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label for="campaign_type" class="form-label required">
                                        <i class="fas fa-layer-group me-1"></i>
                                        نوع الحملة
                                    </label>
                                    <select id="campaign_type" 
                                            name="campaign_type" 
                                            class="form-select @error('campaign_type') is-invalid @enderror" 
                                            required>
                                        <option value="">اختر نوع الحملة</option>
                                        <option value="promotional" {{ old('campaign_type') == 'promotional' ? 'selected' : '' }}>
                                            <i class="fas fa-percent"></i> ترويجية - عروض وخصومات
                                        </option>
                                        <option value="seasonal" {{ old('campaign_type') == 'seasonal' ? 'selected' : '' }}>
                                            <i class="fas fa-calendar-alt"></i> موسمية - مناسبات خاصة
                                        </option>
                                        <option value="loyalty" {{ old('campaign_type') == 'loyalty' ? 'selected' : '' }}>
                                            <i class="fas fa-heart"></i> الولاء - مكافآت العملاء المميزين
                                        </option>
                                        <option value="birthday" {{ old('campaign_type') == 'birthday' ? 'selected' : '' }}>
                                            <i class="fas fa-birthday-cake"></i> أعياد ميلاد العملاء
                                        </option>
                                        <option value="appreciation" {{ old('campaign_type') == 'appreciation' ? 'selected' : '' }}>
                                            <i class="fas fa-thumbs-up"></i> تقدير وشكر
                                        </option>
                                    </select>
                                    @error('campaign_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label for="gift_id" class="form-label required">
                                        <i class="fas fa-gift me-1"></i>
                                        الهدية المراد الترويج لها
                                    </label>
                                    <select id="gift_id" 
                                            name="gift_id" 
                                            class="form-select @error('gift_id') is-invalid @enderror" 
                                            required>
                                        <option value="">اختر الهدية</option>
                                        @foreach($gifts as $gift)
                                            <option value="{{ $gift->id }}" 
                                                    data-price="{{ $gift->price }}"
                                                    data-stock="{{ $gift->stock_quantity }}"
                                                    {{ old('gift_id') == $gift->id ? 'selected' : '' }}>
                                                {{ $gift->name }} - {{ number_format($gift->price, 2) }} ريال
                                                (متوفر: {{ $gift->stock_quantity }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('gift_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-12">
                                    <label for="campaign_description" class="form-label">
                                        <i class="fas fa-align-left me-1"></i>
                                        وصف الحملة
                                    </label>
                                    <textarea id="campaign_description" 
                                              name="campaign_description" 
                                              class="form-control @error('campaign_description') is-invalid @enderror" 
                                              rows="4"
                                              placeholder="اكتب وصفاً مفصلاً للحملة، أهدافها، والجمهور المستهدف...">{{ old('campaign_description') }}</textarea>
                                    @error('campaign_description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        <i class="fas fa-lightbulb me-1"></i>
                                        وصف جيد يساعد في فهم الحملة وأهدافها بشكل أفضل
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Gift Information Preview -->
                        <div class="form-section" id="gift-preview" style="display: none;">
                            <h4 class="section-title">
                                <i class="fas fa-eye section-icon"></i>
                                معاينة الهدية المختارة
                            </h4>
                            
                            <div class="gift-preview-card">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="gift-name mb-1"></h5>
                                        <p class="gift-description text-muted mb-2"></p>
                                        <div class="gift-details">
                                            <span class="badge bg-success me-2">
                                                <i class="fas fa-money-bill-wave me-1"></i>
                                                السعر: <span class="gift-price"></span> ريال
                                            </span>
                                            <span class="badge bg-info">
                                                <i class="fas fa-boxes me-1"></i>
                                                المتوفر: <span class="gift-stock"></span> قطعة
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <div class="gift-image-placeholder">
                                            <i class="fas fa-gift fa-3x text-primary"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="form-actions">
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="{{ route('vendor.campaigns.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    إلغاء والعودة
                                </a>
                                
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    إنشاء الحملة والمتابعة
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.campaign-create-card {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    overflow: hidden;
}

.campaign-create-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 2rem;
}

.form-section {
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.section-title {
    color: #333;
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-icon {
    color: #667eea;
    font-size: 1.2em;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.gift-preview-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    border: 2px dashed #e9ecef;
    transition: all 0.3s ease;
}

.gift-image-placeholder {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 2rem;
    border: 2px dashed #dee2e6;
}

.form-actions {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}
</style>

<script>
document.getElementById('gift_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const preview = document.getElementById('gift-preview');
    
    if (this.value) {
        const giftName = selectedOption.text.split(' - ')[0];
        const giftPrice = selectedOption.getAttribute('data-price');
        const giftStock = selectedOption.getAttribute('data-stock');
        
        document.querySelector('.gift-name').textContent = giftName;
        document.querySelector('.gift-price').textContent = Number(giftPrice).toLocaleString();
        document.querySelector('.gift-stock').textContent = giftStock;
        
        preview.style.display = 'block';
        preview.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    } else {
        preview.style.display = 'none';
    }
});

// Enhanced form validation
document.querySelector('.campaign-form').addEventListener('submit', function(e) {
    const requiredFields = ['campaign_name', 'campaign_type', 'gift_id'];
    let isValid = true;
    
    requiredFields.forEach(field => {
        const element = document.getElementById(field);
        if (!element.value.trim()) {
            element.classList.add('is-invalid');
            isValid = false;
        } else {
            element.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        Swal.fire({
            title: 'بيانات ناقصة!',
            text: 'يرجى ملء جميع الحقول المطلوبة',
            icon: 'warning',
            confirmButtonText: 'حسناً'
        });
    }
});
</script>

@endsection 