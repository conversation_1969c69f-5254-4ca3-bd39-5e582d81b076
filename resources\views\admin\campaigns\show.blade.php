@extends('layouts.admin')

@section('title', 'تفاصيل الحملة الإعلانية')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">تفاصيل الحملة الإعلانية</h1>
            <p class="text-muted">{{ $campaign->campaign_name ?? 'حملة ' . $campaign->gift->name }}</p>
        </div>
        <div>
            <a href="{{ route('admin.campaigns.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للحملات
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Campaign Overview -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات الحملة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">اسم الحملة:</label>
                                <p class="mb-0">{{ $campaign->campaign_name ?? 'غير محدد' }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">نوع الحملة:</label>
                                <p class="mb-0">
                                    @switch($campaign->campaign_type)
                                        @case('promotional')
                                            <i class="fas fa-percent me-1"></i>ترويجية
                                            @break
                                        @case('seasonal')
                                            <i class="fas fa-calendar-alt me-1"></i>موسمية
                                            @break
                                        @case('loyalty')
                                            <i class="fas fa-heart me-1"></i>الولاء
                                            @break
                                        @case('birthday')
                                            <i class="fas fa-birthday-cake me-1"></i>عيد ميلاد
                                            @break
                                        @case('appreciation')
                                            <i class="fas fa-star me-1"></i>تقدير
                                            @break
                                        @default
                                            غير محدد
                                    @endswitch
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">المرحلة الحالية:</label>
                                <span class="badge bg-{{ $campaign->stage == 'completed' ? 'success' : ($campaign->stage == 'failed' ? 'danger' : 'primary') }}">
                                    {{ $campaign->getStageDisplayName() }}
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                                <p class="mb-0">{{ $campaign->created_at->format('Y/m/d H:i') }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">منشئ الحملة:</label>
                                <p class="mb-0">{{ $campaign->creator->name ?? 'غير محدد' }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">التقدم:</label>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ $campaign->getProgressPercentage() }}%"
                                         aria-valuenow="{{ $campaign->getProgressPercentage() }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                                <small class="text-muted">{{ $campaign->getProgressPercentage() }}%</small>
                            </div>
                        </div>
                    </div>
                    
                    @if($campaign->campaign_description)
                        <div class="mb-3">
                            <label class="form-label fw-bold">وصف الحملة:</label>
                            <p class="mb-0">{{ $campaign->campaign_description }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Gift Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-gift me-2"></i>معلومات الهدية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            @if($campaign->gift->image_url)
                                <img src="{{ $campaign->gift->image_url }}" alt="{{ $campaign->gift->name }}" 
                                     class="img-fluid rounded">
                            @else
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 120px;">
                                    <i class="fas fa-image text-muted fa-2x"></i>
                                </div>
                            @endif
                        </div>
                        <div class="col-md-9">
                            <h6 class="fw-bold">{{ $campaign->gift->name }}</h6>
                            <p class="text-muted mb-2">{{ $campaign->gift->description }}</p>
                            <div class="row">
                                <div class="col-sm-6">
                                    <small class="text-muted">السعر:</small>
                                    <p class="mb-1 fw-bold text-success">{{ number_format($campaign->gift->price, 2) }} ريال</p>
                                </div>
                                <div class="col-sm-6">
                                    <small class="text-muted">الفئة:</small>
                                    <p class="mb-1">{{ $campaign->gift->category ?? 'غير محدد' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vendor Information -->
            @if($campaign->vendor)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-store me-2"></i>معلومات التاجر
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-2">
                                <small class="text-muted">اسم الشركة:</small>
                                <p class="mb-1 fw-bold">{{ $campaign->vendor->company_name }}</p>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">الشخص المسؤول:</small>
                                <p class="mb-1">{{ $campaign->vendor->contact_person }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2">
                                <small class="text-muted">رقم الهاتف:</small>
                                <p class="mb-1">{{ $campaign->vendor->phone }}</p>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">البريد الإلكتروني:</small>
                                <p class="mb-1">{{ $campaign->vendor->email }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Campaign Statistics & Actions -->
        <div class="col-lg-4">
            <!-- Campaign Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات الحملة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">{{ number_format($campaign->target_client_count ?? 0) }}</h4>
                                <small class="text-muted">العملاء المستهدفون</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success mb-1">{{ number_format($campaign->message_count ?? 0) }}</h4>
                            <small class="text-muted">عدد الرسائل</small>
                        </div>
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-info mb-1">{{ number_format($campaign->sent_count ?? 0) }}</h4>
                                <small class="text-muted">تم الإرسال</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-danger mb-1">{{ number_format($campaign->failed_count ?? 0) }}</h4>
                            <small class="text-muted">فشل الإرسال</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campaign Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>الإجراءات
                    </h5>
                </div>
                <div class="card-body d-grid gap-2">
                    @if(in_array($campaign->stage, ['gift_info', 'template_selection', 'client_filtering', 'message_count', 'platform_selection', 'payment']))
                        <a href="{{ route('admin.campaigns.wizard', $campaign) }}" class="btn btn-primary">
                            <i class="fas fa-arrow-right me-2"></i>متابعة الإنشاء
                        </a>
                    @endif

                    @if($campaign->stage == 'pending_approval')
                        <form method="POST" action="{{ route('admin.campaigns.approve', $campaign) }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-success w-100" 
                                    onclick="return confirm('هل أنت متأكد من الموافقة على هذه الحملة؟')">
                                <i class="fas fa-check me-2"></i>الموافقة على الحملة
                            </button>
                        </form>
                        
                        <form method="POST" action="{{ route('admin.campaigns.reject', $campaign) }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-danger w-100" 
                                    onclick="return confirm('هل أنت متأكد من رفض هذه الحملة؟')">
                                <i class="fas fa-times me-2"></i>رفض الحملة
                            </button>
                        </form>
                    @endif

                    @if($campaign->stage == 'approved')
                        <form method="POST" action="{{ route('admin.campaigns.execute', $campaign) }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-warning w-100" 
                                    onclick="return confirm('هل أنت متأكد من بدء تنفيذ هذه الحملة؟')">
                                <i class="fas fa-play me-2"></i>بدء التنفيذ
                            </button>
                        </form>
                    @endif

                    @if($campaign->stage == 'completed')
                        <a href="{{ route('admin.campaigns.deliveries', $campaign) }}" class="btn btn-info">
                            <i class="fas fa-truck me-2"></i>التسليمات
                        </a>
                        
                        <a href="{{ route('admin.campaigns.analytics', $campaign) }}" class="btn btn-secondary">
                            <i class="fas fa-chart-bar me-2"></i>التحليلات
                        </a>
                    @endif

                    <form method="POST" action="{{ route('admin.campaigns.destroy', $campaign) }}" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-outline-danger w-100" 
                                onclick="return confirm('هل أنت متأكد من حذف هذه الحملة؟')" 
                                {{ in_array($campaign->stage, ['processing', 'completed']) ? 'disabled' : '' }}>
                            <i class="fas fa-trash me-2"></i>حذف الحملة
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
