<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        $user = Auth::user();
        
        // Debug: Log user details
        \Log::info('User login attempt:', [
            'user_id' => $user->id,
            'email' => $user->email,
            'role' => $user->role,
            'isAdmin' => $user->isAdmin(),
            'isVendor' => $user->isVendor(),
            'isStaff' => $user->isStaff()
        ]);
        
        // Redirect based on user role
        if ($user->isAdmin()) {
            \Log::info('Redirecting to admin dashboard');
            return redirect()->route('admin.dashboard');
        } elseif ($user->isVendor()) {
            \Log::info('Redirecting to vendor dashboard');
            return redirect()->route('vendor.dashboard');
        } elseif ($user->isStaff()) {
            \Log::info('Redirecting to employee dashboard');
            return redirect()->route('employee.dashboard');
        } else {
            \Log::info('Redirecting to default dashboard');
            return redirect()->route('dashboard');
        }
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
