<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Client;
use Carbon\Carbon;

class ClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $clients = [
            [
                'name' => 'أحمد محمد الأحمد',
                'phone' => '966501234567',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'الرياض',
                'neighborhood' => 'العليا',
                'gender' => 'Male',
                'age' => 32,
                'birthdate' => Carbon::now()->subYears(32)->format('Y-m-d'),
                'marital_status' => 'متزوج',
                'health_condition' => 'Healthy',
                'occupation' => 'مهندس برمجيات',
                'status' => 'active',
                'payment_status' => 'paid',
                'amount_paid' => 150.75,
                'payment_method' => 'credit_card',
                'additional_fields' => [
                    'preferred_contact_time' => 'مساء',
                    'interests' => ['تقنية', 'رياضة'],
                    'children_count' => 2
                ]
            ],
            [
                'name' => 'فاطمة عبدالله السالم',
                'phone' => '966502345678',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'جدة',
                'neighborhood' => 'البلد',
                'gender' => 'Female',
                'age' => 28,
                'birthdate' => Carbon::now()->subYears(28)->format('Y-m-d'),
                'marital_status' => 'أعزب',
                'health_condition' => 'Healthy',
                'occupation' => 'معلمة',
                'status' => 'active',
                'payment_status' => 'unpaid',
                'amount_paid' => 0,
                'additional_fields' => [
                    'preferred_contact_time' => 'صباح',
                    'interests' => ['تعليم', 'قراءة'],
                    'education_level' => 'جامعي'
                ]
            ],
            [
                'name' => 'محمد علي الزهراني',
                'phone' => '966503456789',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'الدمام',
                'neighborhood' => 'الشاطئ',
                'gender' => 'Male',
                'age' => 45,
                'birthdate' => Carbon::now()->subYears(45)->format('Y-m-d'),
                'marital_status' => 'متزوج',
                'health_condition' => 'Chronic',
                'occupation' => 'طبيب',
                'status' => 'active',
                'payment_status' => 'paid',
                'amount_paid' => 300.00,
                'payment_method' => 'bank_transfer',
                'additional_fields' => [
                    'preferred_contact_time' => 'مساء',
                    'medical_condition' => 'سكري',
                    'children_count' => 3
                ]
            ],
            [
                'name' => 'سارة خالد المطيري',
                'phone' => '************',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'الرياض',
                'neighborhood' => 'الملز',
                'gender' => 'Female',
                'age' => 35,
                'birthdate' => Carbon::now()->subYears(35)->format('Y-m-d'),
                'marital_status' => 'مطلق',
                'health_condition' => 'Healthy',
                'occupation' => 'محاسبة',
                'status' => 'pending',
                'payment_status' => 'unpaid',
                'amount_paid' => 0,
                'additional_fields' => [
                    'preferred_contact_time' => 'ظهر',
                    'interests' => ['موسيقى', 'سفر'],
                    'children_count' => 1
                ]
            ],
            [
                'name' => 'عبدالرحمن صالح القحطاني',
                'phone' => '966505678901',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'المدينة المنورة',
                'neighborhood' => 'قباء',
                'gender' => 'Male',
                'age' => 55,
                'birthdate' => Carbon::now()->subYears(55)->format('Y-m-d'),
                'marital_status' => 'متزوج',
                'health_condition' => 'Healthy',
                'occupation' => 'مدير مبيعات',
                'status' => 'active',
                'payment_status' => 'paid',
                'amount_paid' => 225.50,
                'payment_method' => 'cash',
                'additional_fields' => [
                    'preferred_contact_time' => 'صباح',
                    'interests' => ['قراءة', 'تجارة'],
                    'children_count' => 4
                ]
            ],
            [
                'name' => 'نورا عبدالعزيز الدوسري',
                'phone' => '966506789012',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'الخبر',
                'neighborhood' => 'العقربية',
                'gender' => 'Female',
                'age' => 42,
                'birthdate' => Carbon::now()->subYears(42)->format('Y-m-d'),
                'marital_status' => 'أرمل',
                'health_condition' => 'Chronic',
                'occupation' => 'ممرضة',
                'status' => 'active',
                'payment_status' => 'paid',
                'amount_paid' => 180.25,
                'payment_method' => 'credit_card',
                'additional_fields' => [
                    'preferred_contact_time' => 'مساء',
                    'medical_condition' => 'ضغط',
                    'children_count' => 2
                ]
            ],
            [
                'name' => 'يوسف فهد الشمري',
                'phone' => '966507890123',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'أبها',
                'neighborhood' => 'المنسك',
                'gender' => 'Male',
                'age' => 26,
                'birthdate' => Carbon::now()->subYears(26)->format('Y-m-d'),
                'marital_status' => 'أعزب',
                'health_condition' => 'Healthy',
                'occupation' => 'مصمم جرافيك',
                'status' => 'active',
                'payment_status' => 'unpaid',
                'amount_paid' => 0,
                'additional_fields' => [
                    'preferred_contact_time' => 'ليل',
                    'interests' => ['تصميم', 'فن'],
                    'education_level' => 'دبلوم'
                ]
            ],
            [
                'name' => 'هند راشد العتيبي',
                'phone' => '966508901234',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'الطائف',
                'neighborhood' => 'الحوية',
                'gender' => 'Female',
                'age' => 39,
                'birthdate' => Carbon::now()->subYears(39)->format('Y-m-d'),
                'marital_status' => 'متزوج',
                'health_condition' => 'Healthy',
                'occupation' => 'صيدلانية',
                'status' => 'inactive',
                'payment_status' => 'paid',
                'amount_paid' => 275.00,
                'payment_method' => 'bank_transfer',
                'additional_fields' => [
                    'preferred_contact_time' => 'صباح',
                    'interests' => ['صحة', 'طبخ'],
                    'children_count' => 3
                ]
            ],
            [
                'name' => 'مشعل تركي الحربي',
                'phone' => '************',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'بريدة',
                'neighborhood' => 'السالمية',
                'gender' => 'Male',
                'age' => 33,
                'birthdate' => Carbon::now()->subYears(33)->format('Y-m-d'),
                'marital_status' => 'متزوج',
                'health_condition' => 'Healthy',
                'occupation' => 'مهندس مدني',
                'status' => 'active',
                'payment_status' => 'paid',
                'amount_paid' => 200.75,
                'payment_method' => 'credit_card',
                'additional_fields' => [
                    'preferred_contact_time' => 'ظهر',
                    'interests' => ['هندسة', 'كرة قدم'],
                    'children_count' => 1
                ]
            ],
            [
                'name' => 'أمل حسن الغامدي',
                'phone' => '966500123456',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'مكة المكرمة',
                'neighborhood' => 'العزيزية',
                'gender' => 'Female',
                'age' => 31,
                'birthdate' => Carbon::now()->subYears(31)->format('Y-m-d'),
                'marital_status' => 'أعزب',
                'health_condition' => 'Healthy',
                'occupation' => 'محامية',
                'status' => 'pending',
                'payment_status' => 'unpaid',
                'amount_paid' => 0,
                'additional_fields' => [
                    'preferred_contact_time' => 'مساء',
                    'interests' => ['قانون', 'قراءة'],
                    'education_level' => 'ماجستير'
                ]
            ],
            [
                'name' => 'عبدالله نايف العنزي',
                'phone' => '966501234568',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'حائل',
                'neighborhood' => 'الصناعية',
                'gender' => 'Male',
                'age' => 48,
                'birthdate' => Carbon::now()->subYears(48)->format('Y-m-d'),
                'marital_status' => 'متزوج',
                'health_condition' => 'Chronic',
                'occupation' => 'صاحب أعمال',
                'status' => 'active',
                'payment_status' => 'paid',
                'amount_paid' => 350.00,
                'payment_method' => 'bank_transfer',
                'additional_fields' => [
                    'preferred_contact_time' => 'صباح',
                    'medical_condition' => 'قلب',
                    'children_count' => 5,
                    'business_type' => 'تجارة'
                ]
            ],
            [
                'name' => 'ريم عبدالمحسن العسيري',
                'phone' => '************',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'خميس مشيط',
                'neighborhood' => 'أحد رفيدة',
                'gender' => 'Female',
                'age' => 24,
                'birthdate' => Carbon::now()->subYears(24)->format('Y-m-d'),
                'marital_status' => 'أعزب',
                'health_condition' => 'Healthy',
                'occupation' => 'طالبة جامعية',
                'status' => 'active',
                'payment_status' => 'unpaid',
                'amount_paid' => 0,
                'additional_fields' => [
                    'preferred_contact_time' => 'ظهر',
                    'interests' => ['دراسة', 'رياضة'],
                    'education_level' => 'بكالوريوس',
                    'university' => 'جامعة الملك خالد'
                ]
            ],
            [
                'name' => 'عمر طلال الجهني',
                'phone' => '966503456780',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'ينبع',
                'neighborhood' => 'الواجهة البحرية',
                'gender' => 'Male',
                'age' => 37,
                'birthdate' => Carbon::now()->subYears(37)->format('Y-m-d'),
                'marital_status' => 'مطلق',
                'health_condition' => 'Healthy',
                'occupation' => 'مهندس بترول',
                'status' => 'active',
                'payment_status' => 'paid',
                'amount_paid' => 290.50,
                'payment_method' => 'credit_card',
                'additional_fields' => [
                    'preferred_contact_time' => 'مساء',
                    'interests' => ['بترول', 'بحر'],
                    'children_count' => 2,
                    'company' => 'أرامكو'
                ]
            ],
            [
                'name' => 'لينا سعد البقمي',
                'phone' => '966504567891',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'الباحة',
                'neighborhood' => 'الزاهر',
                'gender' => 'Female',
                'age' => 29,
                'birthdate' => Carbon::now()->subYears(29)->format('Y-m-d'),
                'marital_status' => 'متزوج',
                'health_condition' => 'Healthy',
                'occupation' => 'مترجمة',
                'status' => 'active',
                'payment_status' => 'unpaid',
                'amount_paid' => 0,
                'additional_fields' => [
                    'preferred_contact_time' => 'صباح',
                    'interests' => ['لغات', 'ثقافة'],
                    'children_count' => 1,
                    'languages' => ['عربي', 'انجليزي', 'فرنسي']
                ]
            ],
            [
                'name' => 'خالد وليد الرشيد',
                'phone' => '966505678902',
                'email' => '<EMAIL>',
                'country' => 'السعودية',
                'city' => 'تبوك',
                'neighborhood' => 'السليمانية',
                'gender' => 'Male',
                'age' => 41,
                'birthdate' => Carbon::now()->subYears(41)->format('Y-m-d'),
                'marital_status' => 'متزوج',
                'health_condition' => 'Chronic',
                'occupation' => 'معلم',
                'status' => 'inactive',
                'payment_status' => 'paid',
                'amount_paid' => 165.25,
                'payment_method' => 'cash',
                'additional_fields' => [
                    'preferred_contact_time' => 'ظهر',
                    'medical_condition' => 'ربو',
                    'children_count' => 3,
                    'subject' => 'رياضيات'
                ]
            ]
        ];

        // Insert the client data
        foreach ($clients as $clientData) {
            Client::create($clientData);
        }

        $this->command->info('تم إنشاء ' . count($clients) . ' عميل بنجاح!');
    }
}
