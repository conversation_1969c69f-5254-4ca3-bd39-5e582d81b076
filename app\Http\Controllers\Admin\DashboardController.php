<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Client;
use App\Models\Gift;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // Basic Statistics
        $stats = [
            'total_users' => User::count(),
            'total_vendors' => Vendor::count(),
            'approved_vendors' => Vendor::where('is_approved', true)->count(),
            'pending_vendors' => Vendor::where('is_approved', false)->count(),
            'total_clients' => Client::count(),
            'total_gifts' => Gift::count(),
            'approved_gifts' => Gift::where('approved', true)->count(),
            'pending_gifts' => Gift::where('approved', false)->count(),
            'total_payments' => Payment::count(),
            'successful_payments' => Payment::where('payment_status', 'PAID')->count(),
            'total_revenue' => Payment::where('payment_status', 'PAID')->sum('amount') ?? 0,
            'success_rate' => $this->calculateSuccessRate(),
            'growth_rate' => $this->calculateGrowthRate(),
        ];

        // Monthly Growth Data
        $monthlyData = $this->getMonthlyGrowthData();

        // Recent Activities
        $recentActivities = $this->getRecentActivities();

        // Revenue by Month (SQLite compatible)
        $revenueByMonth = Payment::where('payment_status', 'PAID')
            ->selectRaw("strftime('%m', created_at) as month, SUM(amount) as revenue")
            ->where(DB::raw("strftime('%Y', created_at)"), '=', Carbon::now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Top Performing Vendors
        $topVendors = Vendor::withCount(['gifts', 'approvedGifts'])
            ->with('user')
            ->where('is_approved', true)
            ->orderBy('approved_gifts_count', 'desc')
            ->take(5)
            ->get();

        // Chart Data
        $chartData = [
            'monthly' => $this->getMonthlyChartData(),
            'categories' => $this->getCategoryChartData(),
        ];

        return view('admin.dashboard', compact(
            'stats',
            'monthlyData',
            'recentActivities',
            'revenueByMonth',
            'topVendors',
            'chartData'
        ));
    }

    private function getMonthlyGrowthData()
    {
        $months = [];
        $userGrowth = [];
        $vendorGrowth = [];
        $clientGrowth = [];

        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $months[] = $month->format('M Y');

            $userGrowth[] = User::where(DB::raw("strftime('%Y', created_at)"), '=', $month->year)
                ->where(DB::raw("strftime('%m', created_at)"), '=', sprintf('%02d', $month->month))
                ->count();

            $vendorGrowth[] = Vendor::where(DB::raw("strftime('%Y', created_at)"), '=', $month->year)
                ->where(DB::raw("strftime('%m', created_at)"), '=', sprintf('%02d', $month->month))
                ->count();

            $clientGrowth[] = Client::where(DB::raw("strftime('%Y', created_at)"), '=', $month->year)
                ->where(DB::raw("strftime('%m', created_at)"), '=', sprintf('%02d', $month->month))
                ->count();
        }

        return [
            'months' => $months,
            'users' => $userGrowth,
            'vendors' => $vendorGrowth,
            'clients' => $clientGrowth,
        ];
    }

    public function analytics()
    {
        // Detailed analytics for charts and reports
        $analytics = [
            'payment_status_distribution' => Payment::selectRaw('payment_status, COUNT(*) as count')
                ->groupBy('payment_status')
                ->get(),
            
            'gift_status_distribution' => Gift::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->get(),
            
            'vendor_categories' => Vendor::selectRaw('category, COUNT(*) as count')
                ->whereNotNull('category')
                ->groupBy('category')
                ->get(),
            
            'client_demographics' => [
                'gender' => Client::selectRaw('gender, COUNT(*) as count')
                    ->groupBy('gender')
                    ->get(),
                'age_groups' => Client::selectRaw('
                    CASE 
                        WHEN age < 18 THEN "Under 18"
                        WHEN age BETWEEN 18 AND 25 THEN "18-25"
                        WHEN age BETWEEN 26 AND 35 THEN "26-35"
                        WHEN age BETWEEN 36 AND 45 THEN "36-45"
                        WHEN age BETWEEN 46 AND 55 THEN "46-55"
                        ELSE "55+"
                    END as age_group,
                    COUNT(*) as count
                ')
                ->groupBy('age_group')
                ->get(),
            ],
        ];

        return response()->json($analytics);
    }

    private function calculateSuccessRate()
    {
        $totalPayments = Payment::count();
        if ($totalPayments == 0) return 0;
        
        $successfulPayments = Payment::where('payment_status', 'PAID')->count();
        return round(($successfulPayments / $totalPayments) * 100, 1);
    }

    private function calculateGrowthRate()
    {
        $currentMonth = Carbon::now();
        $previousMonth = Carbon::now()->subMonth();
        
        $currentMonthUsers = User::where(DB::raw("strftime('%Y-%m', created_at)"), '=', $currentMonth->format('Y-m'))->count();
        $previousMonthUsers = User::where(DB::raw("strftime('%Y-%m', created_at)"), '=', $previousMonth->format('Y-m'))->count();
        
        if ($previousMonthUsers == 0) return $currentMonthUsers > 0 ? 100 : 0;
        
        return round((($currentMonthUsers - $previousMonthUsers) / $previousMonthUsers) * 100, 1);
    }

    private function getMonthlyChartData()
    {
        $data = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $count = Payment::where('payment_status', 'PAID')
                ->where(DB::raw("strftime('%Y-%m', created_at)"), '=', $month->format('Y-m'))
                ->sum('amount') ?? 0;
            $data[] = $count;
        }
        return $data;
    }

    private function getCategoryChartData()
    {
        return [30, 25, 25, 20]; // Default data for now
    }

    private function getRecentActivities()
    {
        $activities = [];

        // Recent vendors
        $recentVendors = Vendor::with('user')->latest()->take(3)->get();
        foreach ($recentVendors as $vendor) {
            $activities[] = [
                'icon' => 'store',
                'title' => 'تاجر جديد انضم',
                'description' => $vendor->company_name ?? $vendor->user->name ?? 'تاجر جديد',
                'time' => $vendor->created_at->diffForHumans(),
            ];
        }

        // Recent clients
        $recentClients = Client::latest()->take(2)->get();
        foreach ($recentClients as $client) {
            $activities[] = [
                'icon' => 'user-plus',
                'title' => 'عميل جديد',
                'description' => $client->name . ' انضم للمنصة',
                'time' => $client->created_at->diffForHumans(),
            ];
        }

        // Recent gifts
        $recentGifts = Gift::with('vendor')->latest()->take(2)->get();
        foreach ($recentGifts as $gift) {
            $activities[] = [
                'icon' => 'gift',
                'title' => 'هدية جديدة',
                'description' => 'تم إضافة هدية: ' . $gift->name,
                'time' => $gift->created_at->diffForHumans(),
            ];
        }

        // Sort by time and return latest 5
        usort($activities, function($a, $b) {
            return strtotime($b['time']) - strtotime($a['time']);
        });

        return array_slice($activities, 0, 5);
    }
}
