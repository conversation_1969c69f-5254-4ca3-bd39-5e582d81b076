@extends('layouts.vendor')

@section('title', 'لوحة التحكم - ' . ($vendor->company_name ?? 'التاجر'))

@push('styles')
<style>
    /* Professional Dashboard Styles */
    .dashboard-container {
        background: transparent;
        min-height: 100vh;
    }

    /* Welcome Hero Section */
    .welcome-hero {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 25px;
        padding: 40px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }

    .welcome-hero::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 107, 53, 0.1), transparent);
        transform: rotate(45deg);
        animation: heroShimmer 4s ease-in-out infinite;
    }

    
    @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
    }

    @keyframes heroShimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .welcome-content {
        position: relative;
        z-index: 2;
    }

    .welcome-title {
        color: white;
        font-weight: 800;
        font-size: 2.5rem;
        margin-bottom: 15px;
        background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .welcome-subtitle {
        color: #b0b0b0;
        font-size: 1.1rem;
        margin-bottom: 30px;
        line-height: 1.6;
    }

    /* Quick Actions */
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }

    .quick-action {
        background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 107, 53, 0.05) 100%);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 15px;
        padding: 20px;
        text-decoration: none;
        color: white;
        transition: all 0.3s ease;
        text-align: center;
    }

    .quick-action:hover {
        color: white;
        background: linear-gradient(135deg, rgba(255, 107, 53, 0.2) 0%, rgba(255, 107, 53, 0.1) 100%);
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
    }

    .quick-action i {
        font-size: 2rem;
        color: var(--accent-color);
        margin-bottom: 10px;
        display: block;
    }

    /* Metric Cards */
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    .metric-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 30px;
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .metric-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 107, 53, 0.3);
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-gradient, linear-gradient(135deg, #ff6b35 0%, #f7931e 100%));
        border-radius: 20px 20px 0 0;
    }

    .metric-card.success::before { background: linear-gradient(135deg, #00d4aa 0%, #00bf95 100%); }
    .metric-card.warning::before { background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%); }
    .metric-card.danger::before { background: linear-gradient(135deg, #ff4757 0%, #c44569 100%); }
    .metric-card.info::before { background: linear-gradient(135deg, #3867d6 0%, #8e44ad 100%); }

    .metric-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .metric-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        background: var(--card-gradient, linear-gradient(135deg, #ff6b35 0%, #f7931e 100%));
        box-shadow: 0 10px 25px rgba(255, 107, 53, 0.3);
    }

    .metric-card.success .metric-icon { 
        background: linear-gradient(135deg, #00d4aa 0%, #00bf95 100%);
        box-shadow: 0 10px 25px rgba(0, 212, 170, 0.3);
    }
    .metric-card.warning .metric-icon { 
        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
        box-shadow: 0 10px 25px rgba(255, 193, 7, 0.3);
    }
    .metric-card.danger .metric-icon { 
        background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
        box-shadow: 0 10px 25px rgba(255, 71, 87, 0.3);
    }
    .metric-card.info .metric-icon { 
        background: linear-gradient(135deg, #3867d6 0%, #8e44ad 100%);
        box-shadow: 0 10px 25px rgba(56, 103, 214, 0.3);
    }

    .metric-value {
        font-size: 2.5rem;
        font-weight: 800;
        color: white;
        margin-bottom: 5px;
    }

    .metric-label {
        color: #b0b0b0;
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 15px;
    }

    .metric-trend {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 0.85rem;
    }

    .trend-up { color: #00d4aa; }
    .trend-down { color: #ff4757; }

    /* Charts Section */
    .charts-section {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 30px;
        margin-bottom: 40px;
    }

    .chart-container {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .chart-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 25px;
    }

    .chart-title {
        color: white;
        font-weight: 700;
        font-size: 1.3rem;
        margin: 0;
    }

    .chart-subtitle {
        color: #888;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .chart-canvas {
        position: relative;
        height: 300px;
        width: 100%;
    }

    /* Activity Feed */
    .activity-section {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    }

    .activity-header {
        color: white;
        font-weight: 700;
        font-size: 1.3rem;
        margin-bottom: 25px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .activity-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .activity-list::-webkit-scrollbar {
        width: 4px;
    }

    .activity-list::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
    }

    .activity-list::-webkit-scrollbar-thumb {
        background: var(--accent-color);
        border-radius: 2px;
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        color: white;
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    }

    .activity-content h6 {
        color: white;
        margin: 0 0 5px 0;
        font-weight: 600;
    }

    .activity-content p {
        color: #888;
        margin: 0;
        font-size: 0.85rem;
    }

    .activity-time {
        color: #666;
        font-size: 0.75rem;
        margin-right: auto;
    }

    /* Analytics Overview */
    .analytics-overview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    .analytics-card {
        background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        border: 1px solid rgba(255, 107, 53, 0.1);
        border-radius: 20px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
    }

    .analytics-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }

    .analytics-number {
        font-size: 2rem;
        font-weight: 800;
        color: var(--accent-color);
        margin-bottom: 10px;
    }

    .analytics-label {
        color: #b0b0b0;
        font-size: 0.9rem;
        font-weight: 600;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .charts-section {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .welcome-title {
            font-size: 2rem;
        }
        
        .metrics-grid {
            grid-template-columns: 1fr;
        }
        
        .quick-actions {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .quick-actions {
            grid-template-columns: 1fr;
        }
    }
</style>
@endpush

@section('content')
<div class="dashboard-container">
    <!-- Welcome Hero Section -->
    <div class="welcome-hero">
        <div class="welcome-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="welcome-title">
                        مرحباً بك، {{ $vendor->company_name ?? 'عزيزي التاجر' }}! 👋
                    </h1>
                    <p class="welcome-subtitle">
                        مرحباً بك في لوحة التحكم الخاصة بك في منصة هدايا السعودية. هنا يمكنك إدارة متجرك، متابعة الطلبات، إدارة الموظفين، ومراقبة أداء أعمالك بكفاءة عالية.
                    </p>
                    
                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <a href="{{ route('vendor.employees.index') }}" class="quick-action">
                            <i class="fas fa-users"></i>
                            <div>إدارة الموظفين</div>
                        </a>
                        
                        <a href="{{ route('vendor.gifts.index') }}" class="quick-action">
                            <i class="fas fa-gift"></i>
                            <div>إدارة الهدايا</div>
                        </a>
                        
                        <a href="{{ route('vendor.campaigns.index') }}" class="quick-action">
                            <i class="fas fa-bullhorn"></i>
                            <div>إدارة الحملات</div>
                        </a>
                        
                        <a href="{{ route('vendor.deliveries.index') }}" class="quick-action">
                            <i class="fas fa-truck"></i>
                            <div>إدارة التسليمات</div>
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-store" style="font-size: 150px; color: rgba(255, 107, 53, 0.3); animation: float 3s ease-in-out infinite;"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Metrics Grid -->
    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-header">
                <div class="metric-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="metric-value">{{ $stats['total_employees'] ?? 0 }}</div>
            <div class="metric-label">إجمالي الموظفين</div>
            <div class="metric-trend trend-up">
                <i class="fas fa-arrow-up"></i>
                <span>+{{ $recentStats['new_employees_this_month'] ?? 0 }} هذا الشهر</span>
            </div>
        </div>

        <div class="metric-card success">
            <div class="metric-header">
                <div class="metric-icon">
                    <i class="fas fa-gift"></i>
                </div>
            </div>
            <div class="metric-value">{{ $stats['approved_gifts'] ?? 0 }}</div>
            <div class="metric-label">الهدايا المعتمدة</div>
            <div class="metric-trend trend-up">
                <i class="fas fa-arrow-up"></i>
                <span>من أصل {{ $stats['total_gifts'] ?? 0 }} هدية</span>
            </div>
        </div>

        <div class="metric-card warning">
            <div class="metric-header">
                <div class="metric-icon">
                    <i class="fas fa-truck"></i>
                </div>
            </div>
            <div class="metric-value">{{ $stats['successful_deliveries'] ?? 0 }}</div>
            <div class="metric-label">التسليمات الناجحة</div>
            <div class="metric-trend trend-up">
                <i class="fas fa-arrow-up"></i>
                <span>+{{ $recentStats['successful_deliveries_this_month'] ?? 0 }} هذا الشهر</span>
            </div>
        </div>

        <div class="metric-card info">
            <div class="metric-header">
                <div class="metric-icon">
                    <i class="fas fa-bullhorn"></i>
                </div>
            </div>
            <div class="metric-value">{{ $stats['active_campaigns'] ?? 0 }}</div>
            <div class="metric-label">الحملات النشطة</div>
            <div class="metric-trend trend-up">
                <i class="fas fa-arrow-up"></i>
                <span>+{{ $recentStats['campaigns_completed_this_month'] ?? 0 }} مكتملة</span>
            </div>
        </div>

        <div class="metric-card success">
            <div class="metric-header">
                <div class="metric-icon">
                    <i class="fas fa-percentage"></i>
                </div>
            </div>
            <div class="metric-value">{{ number_format($stats['delivery_success_rate'] ?? 0, 1) }}%</div>
            <div class="metric-label">معدل نجاح التسليم</div>
            <div class="metric-trend trend-up">
                <i class="fas fa-arrow-up"></i>
                <span>أداء ممتاز</span>
            </div>
        </div>

        <div class="metric-card danger">
            <div class="metric-header">
                <div class="metric-icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
            <div class="metric-value">{{ $overdueDeliveries ?? 0 }}</div>
            <div class="metric-label">التسليمات المتأخرة</div>
            <div class="metric-trend trend-down">
                <i class="fas fa-arrow-down"></i>
                <span>تحتاج متابعة فورية</span>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
        <div class="chart-container">
            <div class="chart-header">
                <div>
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line me-2"></i>
                        أداء التسليمات اليومية
                    </h3>
                    <p class="chart-subtitle">نظرة عامة على أداء التسليمات خلال الأسبوع الماضي</p>
                </div>
            </div>
            <div class="chart-canvas">
                <canvas id="deliveriesChart" width="400" height="200"></canvas>
            </div>
        </div>

        <div class="chart-container">
            <div class="chart-header">
                <div>
                    <h3 class="chart-title">
                        <i class="fas fa-chart-pie me-2"></i>
                        توزيع الحملات
                    </h3>
                    <p class="chart-subtitle">حالة الحملات الحالية</p>
                </div>
            </div>
            <div class="chart-canvas">
                <canvas id="campaignsChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Analytics Overview -->
    <div class="analytics-overview">
        <div class="analytics-card">
            <div class="analytics-number">{{ number_format($stats['delivery_success_rate'] ?? 0, 1) }}%</div>
            <div class="analytics-label">معدل نجاح التسليم</div>
        </div>
        
        <div class="analytics-card">
            <div class="analytics-number">{{ $stats['active_employees'] ?? 0 }}</div>
            <div class="analytics-label">الموظفين النشطين</div>
        </div>
        
        <div class="analytics-card">
            <div class="analytics-number">4.8</div>
            <div class="analytics-label">متوسط تقييم الموظفين</div>
        </div>
        
        <div class="analytics-card">
            <div class="analytics-number">{{ $stats['pending_campaigns'] ?? 0 }}</div>
            <div class="analytics-label">حملات في انتظار الموافقة</div>
        </div>
    </div>

    <!-- Activity Feed -->
    <div class="activity-section">
        <h3 class="activity-header">
            <i class="fas fa-history"></i>
            النشاطات الأخيرة
        </h3>
        
        <div class="activity-list">
            @if(isset($recentDeliveries) && $recentDeliveries->count() > 0)
                @foreach($recentDeliveries->take(5) as $delivery)
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-{{ $delivery->status == 'delivered' ? 'check-circle' : 'truck' }}"></i>
                        </div>
                        <div class="activity-content">
                            <h6>{{ $delivery->status == 'delivered' ? 'تم تسليم الطلب' : 'طلب جديد قيد التسليم' }}</h6>
                            <p>{{ $delivery->client->name ?? 'عميل' }} - {{ $delivery->gift->name ?? 'هدية' }}</p>
                        </div>
                        <div class="activity-time">
                            {{ $delivery->created_at->diffForHumans() }}
                        </div>
                    </div>
                @endforeach
            @else
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="activity-content">
                        <h6>تم إضافة موظف جديد</h6>
                        <p>انضم موظف جديد إلى فريق العمل</p>
                    </div>
                    <div class="activity-time">منذ 5 دقائق</div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div class="activity-content">
                        <h6>تم إضافة هدية جديدة</h6>
                        <p>تم رفع هدية جديدة للمراجعة</p>
                    </div>
                    <div class="activity-time">منذ 15 دقيقة</div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="activity-content">
                        <h6>تم اعتماد حملة جديدة</h6>
                        <p>تم اعتماد حملة "هدايا الصيف" بنجاح</p>
                    </div>
                    <div class="activity-time">منذ 30 دقيقة</div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="activity-content">
                        <h6>تم تسليم الطلب</h6>
                        <p>تم تسليم طلب بنجاح للعميل أحمد محمد</p>
                    </div>
                    <div class="activity-time">منذ ساعة</div>
                </div>
            @endif
        </div>
    </div>
</div>

@endsection

@push('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Deliveries Chart
    const deliveriesCtx = document.getElementById('deliveriesChart').getContext('2d');
    const deliveriesChart = new Chart(deliveriesCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($dailyDeliveries->pluck('date')->map(function($date) { return \Carbon\Carbon::parse($date)->format('d/m'); }) ?? ['1/1', '2/1', '3/1', '4/1', '5/1', '6/1', '7/1']) !!},
            datasets: [{
                label: 'التسليمات',
                data: {!! json_encode($dailyDeliveries->pluck('total') ?? [5, 12, 8, 15, 10, 18, 22]) !!},
                borderColor: '#ff6b35',
                backgroundColor: 'rgba(255, 107, 53, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#ff6b35',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#b0b0b0'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#b0b0b0'
                    }
                }
            }
        }
    });

    // Campaigns Chart
    const campaignsCtx = document.getElementById('campaignsChart').getContext('2d');
    const campaignsChart = new Chart(campaignsCtx, {
        type: 'doughnut',
        data: {
            labels: ['معتمدة', 'قيد المراجعة', 'مكتملة', 'ملغية'],
            datasets: [{
                data: [
                    {{ $stats['active_campaigns'] ?? 5 }},
                    {{ $stats['pending_campaigns'] ?? 3 }},
                    {{ $recentStats['campaigns_completed_this_month'] ?? 8 }},
                    1
                ],
                backgroundColor: [
                    '#00d4aa',
                    '#ffc107',
                    '#ff6b35',
                    '#ff4757'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: '#b0b0b0',
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
});
</script>
@endpush
</code_block_to_apply_changes_from>