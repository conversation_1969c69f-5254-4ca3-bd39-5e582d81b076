<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Models\GiftDelivery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class EmployeeController extends Controller
{
    /**
     * Display a listing of employees
     */
    public function index()
    {
        $vendor = Auth::user()->vendor;
        $employees = Employee::where('vendor_id', $vendor->id)
                    ->with(['deliveries'])
                    ->orderBy('created_at', 'desc')
                    ->get();

        return view('vendor.employees.index', compact('employees'));
    }

    /**
     * Show the form for creating a new employee
     */
    public function create()
    {
        return view('vendor.employees.create');
    }

    /**
     * Store a newly created employee
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'national_id' => 'required|string|max:20|unique:employees,national_id',
            'phone' => 'required|string|max:20|unique:employees',
            'email' => 'required|email|unique:employees',
            'password' => 'required|string|min:8|confirmed',
            'gender' => 'required|in:male,female',
            'birth_date' => 'required|date|before:today',
            'address' => 'required|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
            'position' => 'required|string|max:255',
            'employment_type' => 'required|in:full_time,part_time,contract',
            'hire_date' => 'required|date|before_or_equal:today',
            'salary' => 'nullable|numeric|min:0',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $vendor = Auth::user()->vendor;

        $employeeData = [
            'vendor_id' => $vendor->id,
            'name' => $request->name,
            'national_id' => $request->national_id,
            'phone' => $request->phone,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'gender' => $request->gender,
            'birth_date' => $request->birth_date,
            'address' => $request->address,
            'emergency_contact_name' => $request->emergency_contact_name,
            'emergency_contact_phone' => $request->emergency_contact_phone,
            'position' => $request->position,
            'employment_type' => $request->employment_type,
            'hire_date' => $request->hire_date,
            'salary' => $request->salary,
            'status' => 'pending' // يحتاج موافقة الإدارة
        ];

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            $avatarPath = $request->file('profile_photo')->store('employees/avatars', 'public');
            $employeeData['profile_photo'] = $avatarPath;
        }

        Employee::create($employeeData);

        return redirect()->route('vendor.employees.index')
                        ->with('success', 'تم إضافة الموظف بنجاح! في انتظار موافقة الإدارة.');
    }

    /**
     * Display the specified employee
     */
    public function show(Employee $employee)
    {
        $vendor = Auth::user()->vendor;
        if ($employee->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بعرض هذا الموظف');
        }

        $employee->load(['deliveries.client', 'deliveries.campaign']);

        // Calculate performance metrics
        $totalDeliveries = $employee->deliveries->count();
        $successfulDeliveries = $employee->deliveries->where('status', 'delivered')->count();
        $pendingDeliveries = $employee->deliveries->where('status', 'pending')->count();
        $successRate = $totalDeliveries > 0 ? ($successfulDeliveries / $totalDeliveries) * 100 : 0;

        $performance = [
            'total_deliveries' => $totalDeliveries,
            'successful_deliveries' => $successfulDeliveries,
            'pending_deliveries' => $pendingDeliveries,
            'success_rate' => $successRate
        ];

        return view('vendor.employees.show', compact('employee', 'performance'));
    }

    /**
     * Show the form for editing the specified employee
     */
    public function edit(Employee $employee)
    {
        $vendor = Auth::user()->vendor;
        if ($employee->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بتعديل هذا الموظف');
        }

        return view('vendor.employees.edit', compact('employee'));
    }

    /**
     * Update the specified employee
     */
    public function update(Request $request, Employee $employee)
    {
        $vendor = Auth::user()->vendor;
        if ($employee->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بتعديل هذا الموظف');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'national_id' => 'required|string|max:20|unique:employees,national_id,' . $employee->id,
            'phone' => 'required|string|max:20|unique:employees,phone,' . $employee->id,
            'email' => 'required|email|unique:employees,email,' . $employee->id,
            'gender' => 'required|in:male,female',
            'birth_date' => 'required|date|before:today',
            'address' => 'required|string',
            'emergency_contact_name' => 'required|string|max:255',
            'emergency_contact_phone' => 'required|string|max:20',
            'position' => 'required|string|max:255',
            'employment_type' => 'required|in:full_time,part_time,contract',
            'hire_date' => 'required|date|before_or_equal:today',
            'salary' => 'nullable|numeric|min:0',
            'profile_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $employeeData = [
            'name' => $request->name,
            'national_id' => $request->national_id,
            'phone' => $request->phone,
            'email' => $request->email,
            'gender' => $request->gender,
            'birth_date' => $request->birth_date,
            'address' => $request->address,
            'emergency_contact_name' => $request->emergency_contact_name,
            'emergency_contact_phone' => $request->emergency_contact_phone,
            'position' => $request->position,
            'employment_type' => $request->employment_type,
            'hire_date' => $request->hire_date,
            'salary' => $request->salary
        ];

        // Handle profile photo upload
        if ($request->hasFile('profile_photo')) {
            // Delete old profile photo if exists
            if ($employee->profile_photo) {
                Storage::disk('public')->delete($employee->profile_photo);
            }
            
            $avatarPath = $request->file('profile_photo')->store('employees/avatars', 'public');
            $employeeData['profile_photo'] = $avatarPath;
        }

        $employee->update($employeeData);

        return redirect()->route('vendor.employees.index')
                        ->with('success', 'تم تحديث بيانات الموظف بنجاح!');
    }

    /**
     * Remove the specified employee
     */
    public function destroy(Employee $employee)
    {
        $vendor = Auth::user()->vendor;
        if ($employee->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بحذف هذا الموظف');
        }

        // Delete profile photo if exists
        if ($employee->profile_photo) {
            Storage::disk('public')->delete($employee->profile_photo);
        }

        $employee->delete();

        return redirect()->route('vendor.employees.index')
                        ->with('success', 'تم حذف الموظف بنجاح!');
    }

    /**
     * Update employee status
     */
    public function updateStatus(Request $request, Employee $employee)
    {
        $vendor = Auth::user()->vendor;
        if ($employee->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بتعديل حالة هذا الموظف');
        }

        $request->validate([
            'status' => 'required|in:active,inactive,suspended,pending'
        ]);

        $employee->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة الموظف بنجاح'
        ]);
    }

    /**
     * Display a listing of all deliveries for the vendor
     */
    public function deliveriesIndex()
    {
        $vendor = Auth::user()->vendor;

        $deliveries = GiftDelivery::where('vendor_id', $vendor->id)
                             ->with(['client', 'campaign', 'gift', 'employee'])
                             ->orderBy('created_at', 'desc')
                             ->paginate(20);

        $stats = [
            'total' => GiftDelivery::where('vendor_id', $vendor->id)->count(),
            'pending' => GiftDelivery::where('vendor_id', $vendor->id)->where('status', 'pending')->count(),
            'delivered' => GiftDelivery::where('vendor_id', $vendor->id)->where('status', 'delivered')->count(),
            'cancelled' => GiftDelivery::where('vendor_id', $vendor->id)->where('status', 'cancelled')->count(),
        ];

        return view('vendor.deliveries.index', compact('deliveries', 'stats'));
    }

    /**
     * Show deliveries for employee
     */
    public function deliveries(Employee $employee)
    {
        $vendor = Auth::user()->vendor;
        if ($employee->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بعرض تسليمات هذا الموظف');
        }

        $deliveries = $employee->deliveries()
                              ->with(['client', 'campaign', 'gift'])
                              ->orderBy('created_at', 'desc')
                              ->paginate(20);

        return view('vendor.employees.deliveries', compact('employee', 'deliveries'));
    }

    /**
     * Assign employee to delivery
     */
    public function assignEmployee(Request $request, GiftDelivery $delivery)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'expected_delivery_date' => 'nullable|date'
        ]);

        $vendor = Auth::user()->vendor;
        if ($delivery->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بتعديل هذا التسليم');
        }

        $employee = Employee::find($request->employee_id);
        if ($employee->vendor_id !== $vendor->id) {
            abort(403, 'هذا الموظف غير تابع لك');
        }

        $delivery->update([
            'employee_id' => $request->employee_id,
            'expected_delivery_date' => $request->expected_delivery_date
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تعيين الموظف بنجاح'
        ]);
    }

    /**
     * Mark delivery as delivered
     */
    public function markDelivered(GiftDelivery $delivery)
    {
        $vendor = Auth::user()->vendor;
        if ($delivery->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بتعديل هذا التسليم');
        }

        $delivery->update([
            'status' => 'delivered',
            'delivered_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تأكيد التسليم بنجاح'
        ]);
    }

    /**
     * Mark delivery as cancelled
     */
    public function markCancelled(Delivery $delivery)
    {
        $vendor = Auth::user()->vendor;
        if ($delivery->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بتعديل هذا التسليم');
        }

        $delivery->update([
            'status' => 'cancelled',
            'cancelled_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إلغاء التسليم'
        ]);
    }

    /**
     * Get employees list for AJAX
     */
    public function getEmployeesList()
    {
        $vendor = Auth::user()->vendor;

        $employees = Employee::where('vendor_id', $vendor->id)
                            ->select('id', 'name')
                            ->get();

        return response()->json($employees);
    }
}
