@extends('layouts.vendor')

@section('title', 'متابعة التسليمات')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">
                <i class="fas fa-truck me-2"></i>
                متابعة التسليمات - {{ $campaign->name }}
            </h1>
            <p class="text-muted">
                الهدية: {{ $campaign->gift->name ?? 'غير محدد' }} | 
                تاريخ الحملة: {{ $campaign->created_at->format('d/m/Y') }}
            </p>
        </div>
        <a href="{{ route('vendor.campaigns.index') }}" class="btn btn-secondary">
            العودة للحملات
        </a>
    </div>

    <!-- Stats Row -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3>{{ $campaign->deliveries->count() }}</h3>
                    <p class="mb-0">إجمالي العملاء</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3>{{ $deliveredCount = $campaign->deliveries->where('status', 'delivered')->count() }}</h3>
                    <p class="mb-0">تم التسليم</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3>{{ $pendingCount = $campaign->deliveries->where('status', 'pending')->count() }}</h3>
                    <p class="mb-0">في الانتظار</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h3>{{ $cancelledCount = $campaign->deliveries->where('status', 'cancelled')->count() }}</h3>
                    <p class="mb-0">ملغية</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress -->
    @if($campaign->deliveries->count() > 0)
        @php
            $percentage = ($deliveredCount / $campaign->deliveries->count()) * 100;
        @endphp
        <div class="card mb-4">
            <div class="card-body">
                <h6>معدل إنجاز التسليمات: {{ number_format($percentage, 1) }}%</h6>
                <div class="progress">
                    <div class="progress-bar bg-success" style="width: {{ $percentage }}%"></div>
                </div>
                <small class="text-muted">{{ $deliveredCount }} من {{ $campaign->deliveries->count() }} عميل</small>
            </div>
        </div>
    @endif

    <!-- Deliveries Table -->
    <div class="card">
        <div class="card-header">
            <h5>قائمة العملاء والتسليمات</h5>
        </div>
        <div class="card-body">
            @if($campaign->deliveries->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>الموظف المسؤول</th>
                                <th>حالة التسليم</th>
                                <th>تاريخ التسليم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($campaign->deliveries as $index => $delivery)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>
                                        <strong>{{ $delivery->client->name ?? 'غير محدد' }}</strong>
                                        @if($delivery->client->email)
                                            <br><small class="text-muted">{{ $delivery->client->email }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="tel:{{ $delivery->client->phone }}" class="text-decoration-none">
                                            {{ $delivery->client->phone ?? 'غير محدد' }}
                                        </a>
                                    </td>
                                    <td>
                                        @if($delivery->employee)
                                            <strong>{{ $delivery->employee->name }}</strong>
                                            <br><small>{{ $delivery->employee->phone }}</small>
                                        @else
                                            <span class="text-muted">غير مُعيّن</span>
                                        @endif
                                    </td>
                                    <td>
                                        @switch($delivery->status)
                                            @case('delivered')
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle"></i> تم التسليم
                                                </span>
                                                @break
                                            @case('cancelled')
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times-circle"></i> ملغي
                                                </span>
                                                @break
                                            @default
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock"></i> في الانتظار
                                                </span>
                                        @endswitch
                                    </td>
                                    <td>
                                        @if($delivery->delivered_at)
                                            <strong>{{ $delivery->delivered_at->format('d/m/Y') }}</strong>
                                            <br><small>{{ $delivery->delivered_at->format('H:i') }}</small>
                                        @else
                                            <span class="text-muted">لم يسلم بعد</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            @if($delivery->status === 'pending')
                                                <button class="btn btn-success btn-sm" onclick="markDelivered({{ $delivery->id }})">
                                                    <i class="fas fa-check"></i> تسليم
                                                </button>
                                                <button class="btn btn-danger btn-sm" onclick="markCancelled({{ $delivery->id }})">
                                                    <i class="fas fa-times"></i> إلغاء
                                                </button>
                                            @endif
                                            <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $delivery->client->phone) }}" 
                                               target="_blank" 
                                               class="btn btn-success btn-sm">
                                                <i class="fab fa-whatsapp"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                    <h5>لا توجد تسليمات في هذه الحملة</h5>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function markDelivered(deliveryId) {
    if (confirm('هل أنت متأكد من تأكيد التسليم؟')) {
        fetch(`/vendor/deliveries/${deliveryId}/mark-delivered`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في تأكيد التسليم');
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function markCancelled(deliveryId) {
    if (confirm('هل أنت متأكد من إلغاء التسليم؟')) {
        fetch(`/vendor/deliveries/${deliveryId}/mark-cancelled`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في إلغاء التسليم');
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
@endpush 